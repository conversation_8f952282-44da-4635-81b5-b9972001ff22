<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Billings & Earnings Dashboard</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    /* Reset and base styles */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Poppins', -apple-system, BlinkMacSystemFont, "Se<PERSON><PERSON> UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
      background-color: var(--bg-light);
      color: var(--text-dark);
      line-height: 1.5;
      min-height: 100vh;
    }

    /* Colors */
    :root {
      --primary-blue: #004AAD;
      --primary-pink: #CD208B;
      --yellow: #FFD700;
      --bg-light: #f8f9fa;
      --bg-white: #ffffff;
      --bg-card: #ffffff;
      --border-color: #e5e7eb;
      --text-dark: #000000;
      --text-light: #FFFFFF;
      --text-gray: #666;
      --text-muted: #6b7280;
      --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
      --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --radius-sm: 0.25rem;
      --radius-md: 0.375rem;
      --radius-lg: 0.5rem;
      --radius-full: 9999px;
    }

    /* Layout */
    .container {
      max-width: 2000px;
      margin: 0 auto;
      background-color: white;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    /* Navbar Styles */
    .navbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 2rem;
        background: white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        height: 5rem;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .navbar-left {
        display: flex;
        align-items: center;
        gap: 2rem;
    }

    .logo {
        display: flex;
        align-items: center;
        text-decoration: none;
    }

    .logo img {
        width: 3.5rem;
        height: 3.5rem;
        border-radius: 50%;
        border: 2px solid var(--primary-blue);
        object-fit: cover;
    }

    .logo h1 {
        font-size: 1.7rem;
        font-weight: bold;
        color: var(--primary-pink);
        margin-left: 0.5rem;
    }

    .nav-links {
        display: flex;
        gap: 2rem;
        align-items: center;
    }

    @media (max-width: 768px) {
      .nav-links {
        display: none;
      }
    }

    .nav-links a {
        color: var(--primary-blue);
        text-decoration: none;
        font-size: 1.1rem;
        font-weight: 500;
    }

    .nav-links a:hover {
        color: var(--primary-pink);
    }

    .nav-dropdown {
        position: relative;
        display: inline-block;
    }

    .nav-dropbtn {
        font-weight: 500;
        font-size: 1.1rem;
        color: var(--primary-blue);
        background: none;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0;
    }

    .nav-dropbtn:hover {
        color: var(--primary-pink);
    }

    .nav-dropdown-content {
        display: none;
        position: absolute;
        background-color: #fff;
        min-width: 200px;
        box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        border-radius: 8px;
        z-index: 1001;
        top: 100%;
        left: 0;
    }

    .nav-dropdown-content a {
        color: var(--primary-blue);
        padding: 12px 16px;
        text-decoration: none;
        display: block;
        font-size: 1rem;
    }

    .nav-dropdown-content a:hover {
        background-color: #f9f9f9;
        color: var(--primary-pink);
    }

    .nav-dropdown:hover .nav-dropdown-content {
        display: block;
    }

    /* Right section container */
    .right-section {
        display: flex;
        align-items: center;
        gap: 1.5rem;
    }

    /* Search container */
    .search-container {
        display: flex;
        align-items: center;
    }

    @media (max-width: 768px) {
      .search-container {
        display: none;
      }
    }

    .search-type-select {
        position: relative;
    }

    .search-type-button {
        height: 2.5rem;
        background: white;
        border: 1px solid var(--primary-blue);
        border-right: none;
        border-radius: 8px 0 0 8px;
        padding: 0 1rem;
        color: var(--primary-blue);
        font-size: 1rem;
        display: flex;
        align-items: center;
        cursor: pointer;
    }

    .search-bar {
        height: 2.5rem;
        display: flex;
        align-items: center;
        background: white;
        border: 1px solid var(--primary-blue);
        border-radius: 0 8px 8px 0;
        width: 200px;
    }

    .search-bar input {
        border: none;
        outline: none;
        padding: 0 0.5rem;
        width: 100%;
        height: 100%;
        font-size: 1rem;
    }

    .search-bar .icon {
        color: var(--primary-blue);
        padding: 0 0.5rem;
    }

    /* Auth buttons container */
    .auth-buttons {
        display: flex;
        align-items: center;
        gap: 1.5rem;
    }

    /* Notification icon */
    .notification-icon {
        position: relative;
        cursor: pointer;
    }

    .notification-icon i {
        font-size: 1.5rem;
        color: var(--primary-blue);
    }

    .notification-badge {
        position: absolute;
        top: -8px;
        right: -8px;
        background-color: var(--primary-pink);
        color: white;
        border-radius: 50%;
        padding: 0.1rem 0.4rem;
        font-size: 0.8rem;
        min-width: 18px;
        height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Profile button */
    .profile-button {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        overflow: hidden;
        cursor: pointer;
        border: 2px solid var(--primary-blue);
    }

    .profile-button img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    /* Profile dropdown */
    .profile-dropdown {
        position: relative;
        display: inline-block;
    }

    .profile-dropdown-content {
        display: none;
        position: absolute;
        right: 0;
        top: 50px;
        background-color: #fff;
        min-width: 200px;
        box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        border-radius: 8px;
        z-index: 1001;
    }

    .profile-dropdown-content a {
        color: var(--primary-blue);
        padding: 12px 16px;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 1rem;
    }

    .profile-dropdown-content a i {
        width: 20px;
        text-align: center;
    }

    .profile-dropdown-content a:hover {
        background-color: #f9f9f9;
        color: var(--primary-pink);
    }

    .dropdown-divider {
        height: 1px;
        background-color: #eee;
        margin: 8px 0;
    }

    .logout-option {
        color: #dc3545 !important;
    }

    .logout-option:hover {
        background-color: #fff5f5 !important;
        color: #dc3545 !important;
    }

    /* Show dropdown on hover */
    .profile-dropdown:hover .profile-dropdown-content {
        display: block;
    }

    /* Mobile menu styles */
    .mobile-menu-btn {
        background: none;
        border: none;
        cursor: pointer;
        width: 40px;
        height: 40px;
        border-radius: 6px;
        display: none;
        align-items: center;
        justify-content: center;
    }

    @media (max-width: 768px) {
      .mobile-menu-btn {
        display: flex;
      }
    }

    .mobile-menu-btn:hover {
        background-color: #f3f4f6;
    }

    .mobile-menu {
        display: none;
        position: absolute;
        top: 60px;
        right: 1rem;
        background-color: white;
        border-radius: 6px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        width: 200px;
        z-index: 20;
        border: 1px solid #e5e7eb;
    }

    .mobile-menu.active {
        display: block;
    }

    .mobile-menu a {
        display: block;
        padding: 0.75rem 1rem;
        color: #333;
        font-size: 0.875rem;
        border-bottom: 1px solid #f3f4f6;
    }

    .mobile-menu a:last-child {
        border-bottom: none;
    }

    .mobile-menu a:hover {
        background-color: #f9fafb;
    }

    /* Footer Styles */
    footer {
        background-color: #1a1a1a;
        color: #fff;
        padding: 3rem 0;
        margin-top: 3rem;
    }

    .footer-content {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .footer-section {
        display: flex;
        flex-direction: column;
    }

    .footer-section h3 {
        font-size: 1.25rem;
        margin-bottom: 1.5rem;
        color: #fff;
        font-weight: 600;
    }

    .footer-section a {
        color: #b3b3b3;
        margin-bottom: 0.75rem;
        transition: color 0.2s;
        text-decoration: none;
    }

    .footer-section a:hover {
        color: var(--primary-pink);
    }

    .footer-bottom {
        text-align: center;
        padding-top: 2rem;
        border-top: 1px solid #333;
    }

    .footer-bottom p {
        margin: 1rem 0;
        color: #b3b3b3;
    }

    .footer-bottom a {
        color: #fff;
        transition: color 0.2s;
        text-decoration: none;
    }

    .footer-bottom a:hover {
        color: var(--primary-pink);
    }

    .social-links-container {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .social-links {
        display: flex;
        gap: 1rem;
    }

    .social-links a {
        font-size: 1.25rem;
        color: #fff;
        transition: color 0.2s;
    }

    .social-links a:hover {
        color: var(--primary-pink);
    }

    .footer-links {
        display: flex;
        justify-content: center;
        gap: 2rem;
    }

    @media (max-width: 768px) {
        .footer-links {
            flex-direction: column;
            gap: 0.5rem;
        }
    }

    @media (max-width: 480px) {
        .footer-content {
            grid-template-columns: 1fr;
        }
    }

    .social-icons .bi {
      font-size: 1.2rem;
      color: var(--text-light);
      transition: transform 0.3s ease, color 0.3s ease;
    }

    .social-icons a:hover {
      background-color: var(--primary-pink);
      transform: translateY(-3px);
    }

    /* Add responsive styles for footer */
    @media (max-width: 768px) {
      .footer-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
      }

      .footer-bottom {
        flex-direction: column;
        gap: 20px;
      }
    }

    @media (max-width: 480px) {
      .footer-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }
    }

    /* Main content container */
    .main-content {
      padding: 1.5rem;
      max-width: 1200px;
      margin: 0 auto;
    }

    .flex {
      display: flex;
    }

    .flex-col {
      flex-direction: column;
    }

    .items-center {
      align-items: center;
    }

    .justify-between {
      justify-content: space-between;
    }

    .gap-2 {
      gap: 0.5rem;
    }

    .gap-4 {
      gap: 1rem;
    }

    .gap-6 {
      gap: 1.5rem;
    }

    .w-full {
      width: 100%;
    }

    .w-1\/4 {
      width: 25%;
    }

    .w-3\/4 {
      width: 75%;
    }

    .mb-1 {
      margin-bottom: 0.25rem;
    }

    .mb-2 {
      margin-bottom: 0.5rem;
    }

    .mb-4 {
      margin-bottom: 1rem;
    }

    .mb-6 {
      margin-bottom: 1.5rem;
    }

    .mb-8 {
      margin-bottom: 2rem;
    }

    .ml-1 {
      margin-left: 0.25rem;
    }

    .ml-2 {
      margin-left: 0.5rem;
    }

    .ml-auto {
      margin-left: auto;
    }

    .mr-2 {
      margin-right: 0.5rem;
    }

    .mt-6 {
      margin-top: 1.5rem;
    }

    .p-1 {
      padding: 0.25rem;
    }

    .p-2 {
      padding: 0.5rem;
    }

    .p-4 {
      padding: 1rem;
    }

    .p-6 {
      padding: 1.5rem;
    }

    .p-8 {
      padding: 2rem;
    }

    .px-2 {
      padding-left: 0.5rem;
      padding-right: 0.5rem;
    }

    .px-3 {
      padding-left: 0.75rem;
      padding-right: 0.75rem;
    }

    .px-4 {
      padding-left: 1rem;
      padding-right: 1rem;
    }

    .py-1 {
      padding-top: 0.25rem;
      padding-bottom: 0.25rem;
    }

    .py-2 {
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
    }

    .py-3 {
      padding-top: 0.75rem;
      padding-bottom: 0.75rem;
    }

    .py-4 {
      padding-top: 1rem;
      padding-bottom: 1rem;
    }

    .pt-4 {
      padding-top: 1rem;
    }

    .pt-6 {
      padding-top: 1.5rem;
    }

    .pr-6 {
      padding-right: 1.5rem;
    }

    .pl-10 {
      padding-left: 2.5rem;
    }

    /* Typography */
    .text-xs {
      font-size: 0.75rem;
    }

    .text-sm {
      font-size: 0.875rem;
    }

    .text-lg {
      font-size: 1.125rem;
    }

    .text-xl {
      font-size: 1.25rem;
    }

    .text-2xl {
      font-size: 1.5rem;
    }

    .text-3xl {
      font-size: 1.875rem;
    }

    .text-4xl {
      font-size: 2.25rem;
    }

    .font-medium {
      font-weight: 500;
    }

    .font-bold {
      font-weight: 700;
    }

    .text-center {
      text-align: center;
    }

    .text-right {
      text-align: right;
    }

    .text-white {
      color: #ffffff;
    }

    .text-primary-blue {
      color: var(--primary-blue);
    }

    .text-primary-pink {
      color: var(--primary-pink);
    }

    .text-gray-300 {
      color: #d1d5db;
    }

    .text-gray-400 {
      color: #9ca3af;
    }

    /* Components */
    .card {
      background-color: var(--bg-white);
      border: 1px solid var(--border-color);
      border-radius: var(--radius-lg);
      overflow: hidden;
      box-shadow: var(--shadow-md);
      transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .card:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
    }

    .button {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      border-radius: var(--radius-md);
      padding: 0.625rem 1.25rem;
      font-size: 0.875rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      box-shadow: var(--shadow-sm);
    }

    .button:hover {
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }

    .button:active {
      transform: translateY(0);
    }

    .button-primary {
      background-color: var(--primary-blue);
      color: white;
      border: 1px solid var(--primary-blue);
    }

    .button-primary:hover {
      background-color: rgba(0, 58, 140, 0.9);
      box-shadow: 0 0 15px rgba(0, 58, 140, 0.4);
    }

    .button-secondary {
      background-color: var(--primary-pink);
      color: white;
      border: 1px solid var(--primary-pink);
    }

    .button-secondary:hover {
      background-color: rgba(212, 27, 140, 0.9);
      box-shadow: 0 0 15px rgba(212, 27, 140, 0.4);
    }

    .button-outline {
      background-color: transparent;
      border: 1px solid var(--border-color);
      color: var(--text-dark);
    }

    .button-outline:hover {
      background-color: rgba(0, 0, 0, 0.05);
      border-color: var(--text-dark);
    }

    .button-outline-blue {
      background-color: transparent;
      border: 1px solid var(--primary-blue);
      color: var(--primary-blue);
    }

    .button-outline-blue:hover {
      background-color: rgba(0, 58, 140, 0.1);
      box-shadow: 0 0 10px rgba(0, 58, 140, 0.2);
    }

    .button-outline-pink {
      background-color: transparent;
      border: 1px solid var(--primary-pink);
      color: var(--primary-pink);
    }

    .button-outline-pink:hover {
      background-color: rgba(212, 27, 140, 0.1);
      box-shadow: 0 0 10px rgba(212, 27, 140, 0.2);
    }

    .input {
      width: 100%;
      padding: 0.625rem 0.875rem;
      background-color: var(--bg-white);
      border: 1px solid var(--border-color);
      border-radius: var(--radius-md);
      color: var(--text-dark);
      font-size: 0.875rem;
      transition: all 0.2s ease;
      box-shadow: var(--shadow-sm);
    }

    .input:focus {
      outline: none;
      border-color: var(--primary-blue);
      box-shadow: 0 0 0 3px rgba(0, 58, 140, 0.1);
    }

    .input:hover {
      border-color: #b3b3b3;
    }

    .badge {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      border-radius: var(--radius-full);
      padding: 0.15rem 0.5rem;
      font-size: 0.7rem;
      font-weight: 600;
      line-height: 1;
      letter-spacing: 0.025em;
      text-transform: uppercase;
    }

    .badge-paid {
      background-color: rgba(34, 197, 94, 0.15);
      color: #15803d;
      border: 1px solid rgba(34, 197, 94, 0.3);
    }

    .badge-pending {
      background-color: rgba(234, 179, 8, 0.15);
      color: #a16207;
      border: 1px solid rgba(234, 179, 8, 0.3);
    }

    .badge-overdue {
      background-color: rgba(239, 68, 68, 0.15);
      color: #b91c1c;
      border: 1px solid rgba(239, 68, 68, 0.3);
    }

    .tab-list {
      display: inline-flex;
      background-color: var(--bg-light);
      border: 1px solid var(--border-color);
      border-radius: var(--radius-lg);
      padding: 0.25rem;
      box-shadow: var(--shadow-sm);
    }

    .tab {
      padding: 0.625rem 1.25rem;
      border-radius: var(--radius-md);
      cursor: pointer;
      font-weight: 500;
      transition: all 0.2s ease;
    }

    .tab:hover:not(.active) {
      background-color: rgba(0, 0, 0, 0.05);
    }

    .tab.active {
      background-color: var(--primary-blue);
      color: white;
      box-shadow: var(--shadow-sm);
    }

    .dropdown {
      position: relative;
      display: inline-block;
    }

    .dropdown-content {
      display: none;
      position: absolute;
      background-color: var(--bg-white);
      border: 1px solid var(--border-color);
      border-radius: var(--radius-md);
      min-width: 12rem;
      z-index: 10;
      margin-top: 0.375rem;
      box-shadow: var(--shadow-lg);
      overflow: hidden;
      animation: fadeIn 0.2s ease-out;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(-10px); }
      to { opacity: 1; transform: translateY(0); }
    }

    .dropdown-content.show {
      display: block;
    }

    .dropdown-item {
      padding: 0.625rem 1.25rem;
      cursor: pointer;
      transition: all 0.15s ease;
      color: var(--text-dark);
      font-weight: 500;
    }

    .dropdown-item:hover {
      background-color: var(--bg-light);
      color: var(--primary-blue);
    }

    .select {
      position: relative;
      display: inline-block;
      width: 100%;
    }

    .select-trigger {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      padding: 0.625rem 1rem;
      background-color: var(--bg-white);
      border: 1px solid var(--border-color);
      border-radius: var(--radius-md);
      color: var(--text-dark);
      font-size: 0.875rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      box-shadow: var(--shadow-sm);
    }

    .select-trigger:hover {
      border-color: #b3b3b3;
    }

    .select-content {
      display: none;
      position: absolute;
      background-color: var(--bg-white);
      border: 1px solid var(--border-color);
      border-radius: var(--radius-md);
      width: 100%;
      z-index: 10;
      margin-top: 0.375rem;
      box-shadow: var(--shadow-lg);
      overflow: hidden;
      animation: fadeIn 0.2s ease-out;
    }

    .select-content.show {
      display: block;
    }

    .select-item {
      padding: 0.625rem 1rem;
      cursor: pointer;
      transition: all 0.15s ease;
      color: var(--text-dark);
    }

    .select-item:hover {
      background-color: var(--bg-light);
      color: var(--primary-blue);
    }

    .border-t {
      border-top: 1px solid var(--border-color);
    }

    .border-b {
      border-bottom: 1px solid var(--border-color);
    }

    .border-r {
      border-right: 1px solid var(--border-color);
    }

    .rounded-md {
      border-radius: 0.375rem;
    }

    .rounded-lg {
      border-radius: 0.5rem;
    }

    .rounded-xl {
      border-radius: 0.75rem;
    }

    .rounded-full {
      border-radius: 9999px;
    }

    .grid {
      display: grid;
    }

    .grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }

    .grid-cols-5 {
      grid-template-columns: repeat(5, minmax(0, 1fr));
    }

    .relative {
      position: relative;
    }

    .absolute {
      position: absolute;
    }

    .inset-y-0 {
      top: 0;
      bottom: 0;
    }

    .left-3 {
      left: 0.75rem;
    }

    .top-3 {
      top: 0.75rem;
    }

    .right-0 {
      right: 0;
    }

    .bottom-0 {
      bottom: 0;
    }

    .inline {
      display: inline;
    }

    .inline-block {
      display: inline-block;
    }

    .inline-flex {
      display: inline-flex;
    }

    .h-3 {
      height: 0.75rem;
    }

    .h-4 {
      height: 1rem;
    }

    .h-5 {
      height: 1.25rem;
    }

    .h-6 {
      height: 1.5rem;
    }

    .h-10 {
      height: 2.5rem;
    }

    .h-16 {
      height: 4rem;
    }

    .h-24 {
      height: 6rem;
    }

    .w-3 {
      width: 0.75rem;
    }

    .w-4 {
      width: 1rem;
    }

    .w-5 {
      width: 1.25rem;
    }

    .w-6 {
      width: 1.5rem;
    }

    .w-10 {
      width: 2.5rem;
    }

    .w-16 {
      width: 4rem;
    }

    .w-24 {
      width: 6rem;
    }

    .w-40 {
      width: 10rem;
    }

    .w-64 {
      width: 16rem;
    }

    .min-h-screen {
      min-height: 100vh;
    }

    .space-y-1 > * + * {
      margin-top: 0.25rem;
    }

    .space-y-2 > * + * {
      margin-top: 0.5rem;
    }

    .cursor-pointer {
      cursor: pointer;
    }

    .hover\:underline:hover {
      text-decoration: underline;
    }

    .hover\:bg-gray-800:hover {
      background-color: #1f2937;
    }

    .transition-colors {
      transition-property: color, background-color, border-color;
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
      transition-duration: 150ms;
    }

    /* Icons */
    .icon {
      display: inline-block;
      width: 1em;
      height: 1em;
      stroke-width: 0;
      stroke: currentColor;
      fill: currentColor;
      vertical-align: middle;
    }

    /* Switch */
    .switch {
      position: relative;
      display: inline-block;
      width: 44px;
      height: 24px;
    }

    .switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #333;
      transition: .4s;
      border-radius: 34px;
    }

    .slider:before {
      position: absolute;
      content: "";
      height: 18px;
      width: 18px;
      left: 3px;
      bottom: 3px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }

    input:checked + .slider {
      background-color: var(--primary-pink);
    }

    input:checked + .slider:before {
      transform: translateX(20px);
    }

    /* Hide elements */
    .hidden {
      display: none;
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- Header Section -->
    <nav class="navbar">
        <div class="navbar-left">
            <a href="{{ url_for('landing_page') }}" class="logo">
                <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo">
                <h1>GigGenius</h1>
            </a>
            <div class="nav-links">
                <a href="{{ url_for('genius_page') }}">Find Gigs</a>
                <a href="{{ url_for('my_proposal') }}">Proposals</a>
                <div class="nav-dropdown">
                    <button class="nav-dropbtn">Contracts <i class="bi bi-chevron-down"></i></button>
                    <div class="nav-dropdown-content">
                        <a href="{{ url_for('tracker') }}">Log Works</a>
                    </div>
                </div>
                <div class="nav-dropdown">
                    <button class="nav-dropbtn">Earnings <i class="bi bi-chevron-down"></i></button>
                    <div class="nav-dropdown-content">
                        <a href="{{ url_for('billing_and_earnings') }}">Billings and Earnings</a>
                        <a href="{{ url_for('withdraw_earnings') }}">Withdraw Earnings</a>
                        <a href="{{ url_for('tax_info') }}">Tax Info</a>
                    </div>
                </div>
                <a href="{{ url_for('landing_page') }}">Messages</a>
            </div>
        </div>
        <div class="right-section">
            <div class="search-container">
                <div class="search-type-select">
                    <button id="searchTypeBtn" class="search-type-button">
                        <span id="selectedSearchType">Gigs</span>
                        <i class="bi bi-chevron-down"></i>
                    </button>
                </div>
                <div class="search-bar">
                    <input type="text" id="searchInput" placeholder="Search for gigs...">
                    <div class="icon">
                        <i class="bi bi-search"></i>
                    </div>
                </div>
            </div>
            <div class="auth-buttons">
                <div class="notification-icon">
                    <i class="bi bi-bell"></i>
                    <div class="notification-badge">3</div>
                </div>
                <div class="profile-dropdown">
                    <div class="profile-button">
                        <img src="{{ genius.profile_picture_url if genius else url_for('static', filename='img/default_profile.png') }}" alt="Profile Picture">
                    </div>
                    <div class="profile-dropdown-content">
                        <a href="{{ url_for('genius_profile') }}"><i class="bi bi-person"></i> Your Profile</a>
                        <a href="#"><i class="bi bi-gear"></i> Settings</a>
                        <a href="#"><i class="bi bi-question-circle"></i> Help Center</a>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="logout-option"><i class="bi bi-box-arrow-right"></i> Logout</a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="mobile-menu" id="mobileMenu">
        <a href="{{ url_for('genius_page') }}">Find Gigs</a>
        <a href="{{ url_for('my_proposal') }}">Proposals</a>
        <a href="#">Contracts</a>
        <a href="{{ url_for('tracker') }}">Log Works</a>
        <a href="{{ url_for('billing_and_earnings') }}">Billings and Earnings</a>
        <a href="{{ url_for('withdraw_earnings') }}">Withdraw Earnings</a>
        <a href="{{ url_for('tax_info') }}">Tax Info</a>
        <a href="{{ url_for('landing_page') }}">Messages</a>
        <a href="{{ url_for('genius_profile') }}">Profile</a>
    </div>

    <div class="main-content">
      <div id="app">
    <!-- Dashboard View -->
    <div id="dashboard-view">
      <h1 class="text-4xl font-bold mb-2 text-primary-blue">Billings & Earnings</h1>
      <p class="text-gray-400 mb-6">
        View your earnings and any applicable fees or taxes by client for the past 3 years. For earnings past three
        years, go to
        <a href="#" class="text-primary-pink hover:underline" id="transaction-history-link">
          transaction history
        </a>.
      </p>

      <div class="flex justify-between items-center mb-4">
        <div class="flex gap-2">
          <button class="button button-outline" id="refresh-button">
            <svg class="icon h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"></path>
              <path d="M21 3v5h-5"></path>
              <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"></path>
              <path d="M3 21v-5h5"></path>
            </svg>
            Refresh
          </button>

          <div class="relative">
            <svg class="icon h-4 w-4 absolute left-3 top-3 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="11" cy="11" r="8"></circle>
              <line x1="21" x2="16.65" y1="21" y2="16.65"></line>
            </svg>
            <input type="text" placeholder="Search transactions..." class="input pl-10 w-64" id="search-input">
          </div>

          <div class="dropdown">
            <button class="button button-outline" id="status-filter-button">
              <svg class="icon h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
              </svg>
              <span id="status-filter-text">All Status</span>
              <svg class="icon h-4 w-4 ml-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="6 9 12 15 18 9"></polyline>
              </svg>
            </button>
            <div class="dropdown-content" id="status-filter-dropdown">
              <div class="dropdown-item" data-value="all">All Status</div>
              <div class="dropdown-item" data-value="paid">Paid Only</div>
              <div class="dropdown-item" data-value="pending">Pending Only</div>
              <div class="dropdown-item" data-value="overdue">Overdue Only</div>
            </div>
          </div>
        </div>

        <div class="dropdown">
          <button class="button button-outline" id="date-range-button">
            <svg class="icon h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect>
              <line x1="16" x2="16" y1="2" y2="6"></line>
              <line x1="8" x2="8" y1="2" y2="6"></line>
              <line x1="3" x2="21" y1="10" y2="10"></line>
            </svg>
            <span id="date-range-text">May 1, 2024 - Apr 11, 2025</span>
            <svg class="icon h-4 w-4 ml-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="6 9 12 15 18 9"></polyline>
            </svg>
          </button>
          <div class="dropdown-content" id="date-range-dropdown">
            <div class="dropdown-item" data-value="Last 30 days">Last 30 days</div>
            <div class="dropdown-item" data-value="Last 90 days">Last 90 days</div>
            <div class="dropdown-item" data-value="This year">This year</div>
            <div class="dropdown-item" data-value="May 1, 2024 - Apr 11, 2025">Custom: May 1, 2024 - Apr 11, 2025</div>
          </div>
        </div>
      </div>

      <div class="mb-6">
        <div class="tab-list">
          <div class="tab active" data-tab="billings">Billings & Earnings</div>
          <div class="tab" data-tab="lifetime">Lifetime Billed</div>
        </div>

        <div class="tab-content" id="billings-tab">
          <div class="pt-6">
            <div class="flex justify-between items-center mb-6">
              <div class="dropdown">
                <button class="button button-outline" id="date-range-button-2">
                  <svg class="icon h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect>
                    <line x1="16" x2="16" y1="2" y2="6"></line>
                    <line x1="8" x2="8" y1="2" y2="6"></line>
                    <line x1="3" x2="21" y1="10" y2="10"></line>
                  </svg>
                  <span id="date-range-text-2">May 1, 2024 - Apr 11, 2025</span>
                  <svg class="icon h-4 w-4 ml-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="6 9 12 15 18 9"></polyline>
                  </svg>
                </button>
                <div class="dropdown-content" id="date-range-dropdown-2">
                  <div class="dropdown-item" data-value="Last 30 days">Last 30 days</div>
                  <div class="dropdown-item" data-value="Last 90 days">Last 90 days</div>
                  <div class="dropdown-item" data-value="This year">This year</div>
                  <div class="dropdown-item" data-value="May 1, 2024 - Apr 11, 2025">Custom: May 1, 2024 - Apr 11, 2025</div>
                </div>
              </div>

              <button class="button button-secondary" id="download-csv-button">
                <svg class="icon h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="7 10 12 15 17 10"></polyline>
                  <line x1="12" x2="12" y1="15" y2="3"></line>
                </svg>
                Download CSV
              </button>
            </div>

            <div class="flex gap-6">
              <div class="w-1/4 bg-gray-900 p-4 rounded-lg border border-gray-800">
                <h3 class="font-medium text-primary-blue mb-4">Clients</h3>
                <div id="client-list">
                  <!-- Client list will be populated by JavaScript -->
                </div>
              </div>

              <div class="w-3/4">
                <div class="card mb-6">
                  <div class="p-6">
                    <div class="flex">
                      <div class="flex-1">
                        <h2 class="text-3xl font-bold mb-1 text-primary-blue" id="net-earnings">$0.00</h2>
                        <p class="text-gray-400">Your earnings after fees & taxes</p>

                        <div class="mt-6 space-y-1">
                          <p class="font-medium">
                            Total billed: <span class="text-white" id="total-billed">$0.00</span>
                          </p>
                          <p class="text-gray-400">
                            Total fees: <span class="text-primary-pink" id="total-fees">($0.00)</span>
                          </p>
                        </div>
                      </div>
                      <div class="flex items-center justify-center">
                        <div class="relative">
                          <div class="w-24 h-24 bg-primary-blue/20 rounded-full flex items-center justify-center">
                            <div class="w-16 h-16 bg-primary-blue rounded-full flex items-center justify-center">
                              <span class="text-white text-2xl font-bold">$</span>
                            </div>
                          </div>
                          <div class="absolute -bottom-2 right-0">
                            <div class="w-10 h-10 bg-primary-pink rounded-full flex items-center justify-center text-white font-bold">
                              +
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="card">
                  <div class="p-0">
                    <div class="flex justify-between items-center p-4 border-b border-gray-800">
                      <h3 class="font-medium text-primary-blue">Transaction History</h3>
                      <div class="select">
                        <div class="select-trigger" id="sort-trigger">
                          <span id="sort-value">Newest First</span>
                          <svg class="icon h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="6 9 12 15 18 9"></polyline>
                          </svg>
                        </div>
                        <div class="select-content" id="sort-dropdown">
                          <div class="select-item" data-value="newest">Newest First</div>
                          <div class="select-item" data-value="oldest">Oldest First</div>
                          <div class="select-item" data-value="highest">Highest Amount</div>
                          <div class="select-item" data-value="lowest">Lowest Amount</div>
                        </div>
                      </div>
                    </div>

                    <div class="grid grid-cols-4 gap-4 p-4 font-medium text-gray-400 border-b border-gray-800">
                      <div>Job name</div>
                      <div>Date</div>
                      <div class="text-right">Fees</div>
                      <div class="text-right">Billed</div>
                    </div>

                    <div id="job-list">
                      <!-- Job list will be populated by JavaScript -->
                    </div>

                    <div class="p-4 text-center" id="load-more-container">
                      <button class="button button-outline-blue" id="load-more-button">
                        Load More Transactions
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="tab-content hidden" id="lifetime-tab">
          <div class="pt-6">
            <div class="flex justify-between items-center mb-6">
              <div class="dropdown">
                <button class="button button-outline" id="lifetime-date-range-button">
                  <svg class="icon h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect>
                    <line x1="16" x2="16" y1="2" y2="6"></line>
                    <line x1="8" x2="8" y1="2" y2="6"></line>
                    <line x1="3" x2="21" y1="10" y2="10"></line>
                  </svg>
                  <span id="lifetime-date-range-text">May 1, 2024 - Apr 15, 2025</span>
                  <svg class="icon h-4 w-4 ml-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="6 9 12 15 18 9"></polyline>
                  </svg>
                </button>
                <div class="dropdown-content" id="lifetime-date-range-dropdown">
                  <div class="dropdown-item" data-value="Last 30 days">Last 30 days</div>
                  <div class="dropdown-item" data-value="Last 90 days">Last 90 days</div>
                  <div class="dropdown-item" data-value="This year">This year</div>
                  <div class="dropdown-item" data-value="May 1, 2024 - Apr 15, 2025">Custom: May 1, 2024 - Apr 15, 2025</div>
                </div>
              </div>

              <button class="button button-secondary" id="lifetime-download-csv-button">
                <svg class="icon h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="7 10 12 15 17 10"></polyline>
                  <line x1="12" x2="12" y1="15" y2="3"></line>
                </svg>
                Download CSV
              </button>
            </div>

            <div class="card mb-6">
              <div class="p-6">
                <div class="flex">
                  <div class="flex-1">
                    <h2 class="text-3xl font-bold mb-1 text-primary-blue" id="lifetime-earnings">$76.50</h2>
                    <p class="text-gray-400">Your earnings after GigGenius fees & taxes</p>

                    <div class="mt-6 space-y-1">
                      <p class="font-medium">
                        Total billed: <span class="text-white" id="lifetime-total-billed">$85.00</span>
                      </p>
                      <p class="text-gray-400">
                        Total fees: <span class="text-primary-pink" id="lifetime-total-fees">($8.50)</span>
                      </p>
                    </div>
                  </div>
                  <div class="flex items-center justify-center">
                    <div class="relative">
                      <div class="w-24 h-24 bg-primary-blue/20 rounded-full flex items-center justify-center">
                        <div class="w-16 h-16 bg-primary-blue rounded-full flex items-center justify-center">
                          <span class="text-white text-2xl font-bold">$</span>
                        </div>
                      </div>
                      <div class="absolute -bottom-2 right-0">
                        <div class="w-10 h-10 bg-primary-pink rounded-full flex items-center justify-center text-white font-bold">
                          +
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="card">
              <div class="p-0">
                <div class="flex justify-between items-center p-4 border-b border-gray-800">
                  <h3 class="font-medium text-primary-blue">Client List</h3>
                  <div class="select">
                    <div class="select-trigger" id="lifetime-sort-trigger">
                      <span id="lifetime-sort-value">Alphabetical</span>
                      <svg class="icon h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="6 9 12 15 18 9"></polyline>
                      </svg>
                    </div>
                    <div class="select-content" id="lifetime-sort-dropdown">
                      <div class="select-item" data-value="alphabetical">Alphabetical</div>
                      <div class="select-item" data-value="highest">Highest Earnings</div>
                      <div class="select-item" data-value="lowest">Lowest Earnings</div>
                    </div>
                  </div>
                </div>

                <div id="lifetime-client-list" class="p-4">
                  <!-- Client list will be populated by JavaScript -->
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Client Detail View (initially hidden) -->
        <div class="tab-content hidden" id="client-detail-tab">
          <div class="pt-6">
            <div class="flex justify-between items-center mb-6">
              <button class="button button-outline-blue" id="back-to-lifetime">
                ← Back to Lifetime Billed
              </button>
              <button class="button button-secondary" id="client-download-csv-button">
                <svg class="icon h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="7 10 12 15 17 10"></polyline>
                  <line x1="12" x2="12" y1="15" y2="3"></line>
                </svg>
                Download CSV
              </button>
            </div>

            <div class="card mb-6">
              <div class="p-6">
                <div class="flex">
                  <div class="flex-1">
                    <h2 class="text-3xl font-bold mb-1 text-primary-blue" id="client-name-header">Client Name</h2>
                    <p class="text-gray-400">Client earnings summary</p>

                    <div class="mt-4 space-y-2">
                      <p>
                        Total billed: <span id="client-total-billed">$0.00</span>
                      </p>
                      <p class="text-gray-400">
                        Total fees: <span class="text-primary-pink" id="client-total-fees">($0.00)</span>
                      </p>
                      <p>
                        Net earnings: <span id="client-net-earnings">$0.00</span>
                      </p>
                    </div>
                  </div>
                  <div class="flex items-center justify-center">
                    <div class="relative">
                      <div class="w-24 h-24 bg-primary-blue/20 rounded-full flex items-center justify-center">
                        <div class="w-16 h-16 bg-primary-blue rounded-full flex items-center justify-center">
                          <span class="text-white text-2xl font-bold" id="client-initials">CL</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="card">
              <div class="p-0">
                <div class="flex justify-between items-center p-4 border-b border-gray-800">
                  <h3 class="font-medium text-primary-blue">Transaction History</h3>
                  <div class="select">
                    <div class="select-trigger" id="client-sort-trigger">
                      <span id="client-sort-value">Newest First</span>
                      <svg class="icon h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="6 9 12 15 18 9"></polyline>
                      </svg>
                    </div>
                    <div class="select-content" id="client-sort-dropdown">
                      <div class="select-item" data-value="newest">Newest First</div>
                      <div class="select-item" data-value="oldest">Oldest First</div>
                      <div class="select-item" data-value="highest">Highest Amount</div>
                      <div class="select-item" data-value="lowest">Lowest Amount</div>
                    </div>
                  </div>
                </div>

                <div class="grid grid-cols-4 gap-4 p-4 font-medium text-gray-400 border-b border-gray-800">
                  <div>Job name</div>
                  <div>Date</div>
                  <div class="text-right">Fees</div>
                  <div class="text-right">Billed</div>
                </div>

                <div id="client-job-list">
                  <!-- Client job list will be populated by JavaScript -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Transaction History View -->
    <div id="transaction-history-view" class="hidden">
      <div class="flex justify-between items-center mb-8">
        <h1 class="text-4xl font-bold text-primary-blue">Transaction history</h1>
        <div class="flex items-center gap-3">
          <span class="text-gray-300">New design</span>
          <svg class="icon h-4 w-4 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" x2="12" y1="16" y2="12"></line>
            <line x1="12" x2="12.01" y1="8" y2="8"></line>
          </svg>
          <div class="flex items-center gap-2">
            <label for="new-design" class="text-gray-300">
              On
            </label>
            <label class="switch">
              <input type="checkbox" id="new-design" checked>
              <span class="slider"></span>
            </label>
          </div>
        </div>
      </div>

      <div class="flex justify-between mb-8">
        <div class="w-3/4">
          <button class="button button-outline-blue mb-4" id="back-to-dashboard">
            ← Back to Dashboard
          </button>
        </div>
        <div class="card bg-gray-900 border border-gray-800 p-4 w-1/4 rounded-xl">
          <div class="flex justify-between items-center mb-1">
            <span class="text-gray-300">Available balance:</span>
            <span class="text-primary-pink font-medium" id="available-balance">
              +$0.00
              <svg class="icon h-4 w-4 inline" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </span>
          </div>
          <div class="flex items-center gap-1 text-sm text-gray-400">
            <span id="pending-amount">$0.00 pending</span>
            <svg class="icon h-3 w-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" x2="12" y1="16" y2="12"></line>
              <line x1="12" x2="12.01" y1="8" y2="8"></line>
            </svg>
          </div>
        </div>
      </div>

      <div class="border-t border-gray-800 pt-6 mb-6">
        <div class="grid grid-cols-4 gap-4 mb-4">
          <div>
            <p class="text-gray-400 mb-2">Date range</p>
            <div class="flex items-center border border-gray-700 rounded-md p-2 bg-black">
              <span id="th-date-range">All time</span>
              <svg class="icon ml-auto h-5 w-5 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect>
                <line x1="16" x2="16" y1="2" y2="6"></line>
                <line x1="8" x2="8" y1="2" y2="6"></line>
                <line x1="3" x2="21" y1="10" y2="10"></line>
              </svg>
            </div>
          </div>
          <div>
            <p class="text-gray-400 mb-2">Transaction type</p>
            <div class="select">
              <div class="select-trigger" id="transaction-type-trigger">
                <span id="transaction-type-value">All types</span>
                <svg class="icon h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polyline points="6 9 12 15 18 9"></polyline>
                </svg>
              </div>
              <div class="select-content" id="transaction-type-dropdown">
                <div class="select-item" data-value="All types">All types</div>
                <div class="select-item" data-value="Hourly">Hourly</div>
                <div class="select-item" data-value="Fixed">Fixed</div>
                <div class="select-item" data-value="Subscription">Subscription</div>
                <div class="select-item" data-value="Connects">Connects</div>
              </div>
            </div>
          </div>
          <div>
            <p class="text-gray-400 mb-2">Client</p>
            <div class="select">
              <div class="select-trigger" id="client-filter-trigger">
                <span id="client-filter-value">All clients</span>
                <svg class="icon h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polyline points="6 9 12 15 18 9"></polyline>
                </svg>
              </div>
              <div class="select-content" id="client-filter-dropdown">
                <div class="select-item" data-value="All clients">All clients</div>
                <div class="select-item" data-value="Aptus Bio Science">Aptus Bio Science</div>
                <div class="select-item" data-value="Maria Santos">Maria Santos</div>
                <div class="select-item" data-value="Global Tech Solutions">Global Tech Solutions</div>
              </div>
            </div>
          </div>
          <div>
            <p class="text-gray-400 mb-2">Contract</p>
            <div class="select">
              <div class="select-trigger" id="contract-filter-trigger">
                <span id="contract-filter-value">All contracts</span>
                <svg class="icon h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polyline points="6 9 12 15 18 9"></polyline>
                </svg>
              </div>
              <div class="select-content" id="contract-filter-dropdown">
                <div class="select-item" data-value="All contracts">All contracts</div>
                <div class="select-item" data-value="Technical & Administrative Support">Technical & Administrative Support</div>
                <div class="select-item" data-value="Website Redesign">Website Redesign</div>
                <div class="select-item" data-value="Mobile App Development">Mobile App Development</div>
                <div class="select-item" data-value="Logo Design">Logo Design</div>
              </div>
            </div>
          </div>
        </div>

        <div class="flex justify-between items-center mb-6">
          <div class="border-r border-gray-700 pr-6">
            <h3 class="text-lg font-medium mb-1">Filtered totals</h3>
            <p class="text-gray-400 text-sm">
              Select a filter to get a breakdown of your earnings, fees, and taxes.
            </p>
          </div>
          <div class="ml-6">
            <button class="button button-outline-pink">
              Select download
              <svg class="icon ml-2 h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="6 9 12 15 18 9"></polyline>
              </svg>
            </button>
          </div>
        </div>

        <div class="border-t border-gray-800 pt-4">
          <div class="grid grid-cols-5 gap-4 py-3 text-gray-400">
            <div class="flex items-center">
              Date
              <svg class="icon ml-1 h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" x2="12" y1="16" y2="12"></line>
                <line x1="12" x2="12.01" y1="8" y2="8"></line>
              </svg>
            </div>
            <div>Type</div>
            <div>Contract / Details</div>
            <div>Client</div>
            <div class="text-right">Amount</div>
          </div>

          <div id="transaction-list">
            <!-- Transaction list will be populated by JavaScript -->
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Data
    const clients = ["Maria Santos", "Global Tech Solutions", "Carlos Rodriguez", "Elena Fernandez"];

    const mariaSantosJobs = [
      {
        id: "MS-001",
        name: "Website Redesign",
        fees: 3.0,
        billed: 30.0,
        date: "Apr 5, 2024",
        status: "paid",
        client: "Maria Santos",
      },
      {
        id: "MS-002",
        name: "Logo Design",
        fees: 2.5,
        billed: 25.0,
        date: "Mar 28, 2024",
        status: "paid",
        client: "Maria Santos",
      },
      {
        id: "MS-003",
        name: "Brand Guidelines",
        fees: 4.0,
        billed: 40.0,
        date: "Mar 15, 2024",
        status: "paid",
        client: "Maria Santos",
      },
      {
        id: "MS-004",
        name: "Social Media Graphics",
        fees: 2.0,
        billed: 20.0,
        date: "Mar 5, 2024",
        status: "paid",
        client: "Maria Santos",
      },
    ];

    const globalTechJobs = [
      {
        id: "GT-001",
        name: "Mobile App Development",
        fees: 8.0,
        billed: 80.0,
        date: "Apr 10, 2024",
        status: "pending",
        client: "Global Tech Solutions",
      },
      {
        id: "GT-002",
        name: "API Integration",
        fees: 5.0,
        billed: 50.0,
        date: "Apr 2, 2024",
        status: "paid",
        client: "Global Tech Solutions",
      },
      {
        id: "GT-003",
        name: "Database Migration",
        fees: 6.0,
        billed: 60.0,
        date: "Mar 15, 2024",
        status: "paid",
        client: "Global Tech Solutions",
      },
      {
        id: "GT-004",
        name: "Security Audit",
        fees: 7.0,
        billed: 70.0,
        date: "Mar 1, 2024",
        status: "paid",
        client: "Global Tech Solutions",
      },
    ];

    const carlosJobs = [
      {
        id: "CR-001",
        name: "UI/UX Consultation",
        fees: 4.5,
        billed: 45.0,
        date: "Apr 8, 2024",
        status: "paid",
        client: "Carlos Rodriguez",
      },
      {
        id: "CR-002",
        name: "Wireframe Design",
        fees: 3.0,
        billed: 30.0,
        date: "Mar 25, 2024",
        status: "overdue",
        client: "Carlos Rodriguez",
      },
      {
        id: "CR-003",
        name: "User Testing",
        fees: 5.0,
        billed: 50.0,
        date: "Mar 10, 2024",
        status: "paid",
        client: "Carlos Rodriguez",
      },
    ];

    const elenaJobs = [
      {
        id: "EF-001",
        name: "Content Writing",
        fees: 2.0,
        billed: 20.0,
        date: "Apr 12, 2024",
        status: "pending",
        client: "Elena Fernandez",
      },
      {
        id: "EF-002",
        name: "SEO Optimization",
        fees: 3.5,
        billed: 35.0,
        date: "Apr 1, 2024",
        status: "paid",
        client: "Elena Fernandez",
      },
      {
        id: "EF-003",
        name: "Social Media Strategy",
        fees: 4.0,
        billed: 40.0,
        date: "Mar 20, 2024",
        status: "paid",
        client: "Elena Fernandez",
      },
      {
        id: "EF-004",
        name: "Email Campaign",
        fees: 2.5,
        billed: 25.0,
        date: "Mar 10, 2024",
        status: "paid",
        client: "Elena Fernandez",
      },
    ];

    const transactions = [
      {
        id: "tx-001",
        date: "Apr 9, 2025",
        type: "Hourly",
        contract: "Technical & Administrative Support Specialist (Part-Time)",
        details: "Earnings for Mar 24-Mar 30, 2025",
        client: "Aptus Bio Science",
        clientInitials: "AB",
        amount: 15.6,
      },
      {
        id: "tx-002",
        date: "Mar 26, 2025",
        type: "Subscription",
        contract: "Membership fee",
        client: "",
        amount: -19.99,
      },
      {
        id: "tx-003",
        date: "Mar 26, 2025",
        type: "Hourly",
        contract: "Technical & Administrative Support Specialist (Part-Time)",
        details: "Earnings for Mar 10-Mar 16, 2025",
        client: "Aptus Bio Science",
        clientInitials: "AB",
        amount: 27.3,
      },
      {
        id: "tx-004",
        date: "Mar 20, 2025",
        type: "Connects",
        contract: "10 Connects",
        client: "",
        amount: -1.5,
      },
      {
        id: "tx-005",
        date: "Mar 4, 2025",
        type: "Connects",
        contract: "10 Connects",
        client: "",
        amount: -1.5,
      },
      {
        id: "tx-006",
        date: "Feb 28, 2025",
        type: "Hourly",
        contract: "Website Redesign",
        details: "Earnings for Feb 15-Feb 21, 2025",
        client: "Maria Santos",
        clientInitials: "MS",
        amount: 30.0,
      },
      {
        id: "tx-007",
        date: "Feb 15, 2025",
        type: "Hourly",
        contract: "Mobile App Development",
        details: "Earnings for Feb 1-Feb 7, 2025",
        client: "Global Tech Solutions",
        clientInitials: "GT",
        amount: 80.0,
      },
      {
        id: "tx-008",
        date: "Feb 10, 2025",
        type: "Fixed",
        contract: "Logo Design",
        client: "Maria Santos",
        clientInitials: "MS",
        amount: 25.0,
      },
    ];

    // State
    let state = {
      view: "dashboard", // "dashboard" or "transaction-history"
      dashboard: {
        activeTab: "billings", // "billings", "lifetime", or "client-detail"
        dateRange: "May 1, 2024 - Apr 11, 2025",
        searchQuery: "",
        selectedClient: "Maria Santos",
        statusFilter: "all",
        sortOrder: "newest",
        showAllTransactions: false,
      },
      lifetime: {
        dateRange: "May 1, 2024 - Apr 15, 2025",
        sortOrder: "alphabetical",
      },
      clientDetail: {
        client: "",
        sortOrder: "newest",
      },
      transactionHistory: {
        newDesign: true,
        dateRange: "All time",
        transactionType: "All types",
        clientFilter: "All clients",
        contractFilter: "All contracts",
      }
    };

    // DOM Elements
    const dashboardView = document.getElementById("dashboard-view");
    const transactionHistoryView = document.getElementById("transaction-history-view");
    const transactionHistoryLink = document.getElementById("transaction-history-link");
    const backToDashboardButton = document.getElementById("back-to-dashboard");
    const tabs = document.querySelectorAll(".tab");
    const tabContents = document.querySelectorAll(".tab-content");
    const clientList = document.getElementById("client-list");
    const jobList = document.getElementById("job-list");
    const transactionList = document.getElementById("transaction-list");
    const loadMoreButton = document.getElementById("load-more-button");
    const loadMoreContainer = document.getElementById("load-more-container");
    const netEarnings = document.getElementById("net-earnings");
    const totalBilled = document.getElementById("total-billed");
    const totalFees = document.getElementById("total-fees");
    const searchInput = document.getElementById("search-input");
    const refreshButton = document.getElementById("refresh-button");
    const downloadCsvButton = document.getElementById("download-csv-button");
    const statusFilterButton = document.getElementById("status-filter-button");
    const statusFilterText = document.getElementById("status-filter-text");
    const statusFilterDropdown = document.getElementById("status-filter-dropdown");
    const dateRangeButton = document.getElementById("date-range-button");
    const dateRangeText = document.getElementById("date-range-text");
    const dateRangeDropdown = document.getElementById("date-range-dropdown");
    const dateRangeButton2 = document.getElementById("date-range-button-2");
    const dateRangeText2 = document.getElementById("date-range-text-2");
    const dateRangeDropdown2 = document.getElementById("date-range-dropdown-2");
    const sortTrigger = document.getElementById("sort-trigger");
    const sortValue = document.getElementById("sort-value");
    const sortDropdown = document.getElementById("sort-dropdown");
    const availableBalance = document.getElementById("available-balance");
    const pendingAmount = document.getElementById("pending-amount");
    const thDateRange = document.getElementById("th-date-range");
    const transactionTypeTrigger = document.getElementById("transaction-type-trigger");
    const transactionTypeValue = document.getElementById("transaction-type-value");
    const transactionTypeDropdown = document.getElementById("transaction-type-dropdown");
    const clientFilterTrigger = document.getElementById("client-filter-trigger");
    const clientFilterValue = document.getElementById("client-filter-value");
    const clientFilterDropdown = document.getElementById("client-filter-dropdown");
    const contractFilterTrigger = document.getElementById("contract-filter-trigger");
    const contractFilterValue = document.getElementById("contract-filter-value");
    const contractFilterDropdown = document.getElementById("contract-filter-dropdown");
    const newDesignSwitch = document.getElementById("new-design");

    // Lifetime tab elements
    const lifetimeClientList = document.getElementById("lifetime-client-list");
    const lifetimeEarnings = document.getElementById("lifetime-earnings");
    const lifetimeTotalBilled = document.getElementById("lifetime-total-billed");
    const lifetimeTotalFees = document.getElementById("lifetime-total-fees");
    const lifetimeDateRangeButton = document.getElementById("lifetime-date-range-button");
    const lifetimeDateRangeText = document.getElementById("lifetime-date-range-text");
    const lifetimeDateRangeDropdown = document.getElementById("lifetime-date-range-dropdown");
    const lifetimeSortTrigger = document.getElementById("lifetime-sort-trigger");
    const lifetimeSortValue = document.getElementById("lifetime-sort-value");
    const lifetimeSortDropdown = document.getElementById("lifetime-sort-dropdown");
    const lifetimeDownloadCsvButton = document.getElementById("lifetime-download-csv-button");

    // Client detail tab elements
    const clientDetailTab = document.getElementById("client-detail-tab");
    const backToLifetimeButton = document.getElementById("back-to-lifetime");
    const clientNameHeader = document.getElementById("client-name-header");
    const clientInitials = document.getElementById("client-initials");
    const clientTotalBilled = document.getElementById("client-total-billed");
    const clientTotalFees = document.getElementById("client-total-fees");
    const clientNetEarnings = document.getElementById("client-net-earnings");
    const clientJobList = document.getElementById("client-job-list");
    const clientSortTrigger = document.getElementById("client-sort-trigger");
    const clientSortValue = document.getElementById("client-sort-value");
    const clientSortDropdown = document.getElementById("client-sort-dropdown");
    const clientDownloadCsvButton = document.getElementById("client-download-csv-button");

    // Helper Functions
    function getJobData() {
      switch (state.dashboard.selectedClient) {
        case "Maria Santos":
          return mariaSantosJobs;
        case "Global Tech Solutions":
          return globalTechJobs;
        case "Carlos Rodriguez":
          return carlosJobs;
        case "Elena Fernandez":
          return elenaJobs;
        default:
          return mariaSantosJobs;
      }
    }

    function getFilteredAndSortedJobs() {
      let filteredJobs = getJobData();

      // Apply status filter
      if (state.dashboard.statusFilter !== "all") {
        filteredJobs = filteredJobs.filter((job) => job.status === state.dashboard.statusFilter);
      }

      // Apply search filter
      if (state.dashboard.searchQuery) {
        const query = state.dashboard.searchQuery.toLowerCase();
        filteredJobs = filteredJobs.filter(
          (job) => job.name.toLowerCase().includes(query) || job.date.toLowerCase().includes(query)
        );
      }

      // Apply sorting
      return filteredJobs.sort((a, b) => {
        switch (state.dashboard.sortOrder) {
          case "newest":
            return new Date(b.date).getTime() - new Date(a.date).getTime();
          case "oldest":
            return new Date(a.date).getTime() - new Date(b.date).getTime();
          case "highest":
            return b.billed - a.billed;
          case "lowest":
            return a.billed - b.billed;
          default:
            return 0;
        }
      });
    }

    function updateDashboard() {
      // Update client list
      clientList.innerHTML = "";
      clients.forEach(client => {
        const clientItem = document.createElement("div");
        clientItem.className = `py-3 px-3 rounded-md cursor-pointer transition-colors ${
          client === state.dashboard.selectedClient
            ? "bg-primary-blue/20 text-primary-blue font-medium"
            : "text-gray-400"
        }`;

        if (client !== state.dashboard.selectedClient) {
          clientItem.style.transition = "background-color 0.2s ease";
          clientItem.addEventListener("mouseover", () => {
            clientItem.style.backgroundColor = "rgba(0, 58, 140, 0.1)";
          });
          clientItem.addEventListener("mouseout", () => {
            clientItem.style.backgroundColor = "";
          });
        }
        clientItem.textContent = client;
        clientItem.addEventListener("click", () => {
          state.dashboard.selectedClient = client;
          state.dashboard.statusFilter = "all";
          state.dashboard.searchQuery = "";
          state.dashboard.showAllTransactions = false;
          updateDashboard();
        });
        clientList.appendChild(clientItem);
      });

      // Get filtered and sorted jobs
      const allJobs = getFilteredAndSortedJobs();
      const jobData = state.dashboard.showAllTransactions ? allJobs : allJobs.slice(0, 2);

      // Calculate totals
      const totalBilledValue = jobData.reduce((sum, job) => sum + job.billed, 0);
      const totalFeesValue = jobData.reduce((sum, job) => sum + job.fees, 0);
      const netEarningsValue = totalBilledValue - totalFeesValue;

      // Update summary
      netEarnings.textContent = `$${netEarningsValue.toFixed(2)}`;
      totalBilled.textContent = `$${totalBilledValue.toFixed(2)}`;
      totalFees.textContent = `($${totalFeesValue.toFixed(2)})`;

      // Update job list
      jobList.innerHTML = "";
      if (jobData.length > 0) {
        jobData.forEach(job => {
          const jobItem = document.createElement("div");
          jobItem.className = "grid grid-cols-4 gap-4 p-4 border-b border-gray-800";
          jobItem.style.transition = "background-color 0.2s ease";
          jobItem.addEventListener("mouseover", () => {
            jobItem.style.backgroundColor = "rgba(0, 58, 140, 0.1)";
          });
          jobItem.addEventListener("mouseout", () => {
            jobItem.style.backgroundColor = "";
          });

          const nameCell = document.createElement("div");
          nameCell.className = "font-medium flex items-center";
          const jobNameDiv = document.createElement("div");
          jobNameDiv.className = "flex flex-col";

          const badgeSpan = document.createElement("span");
          badgeSpan.className = `badge ${
            job.status === "paid"
              ? "badge-paid"
              : job.status === "pending"
                ? "badge-pending"
                : "badge-overdue"
          }`;
          badgeSpan.style.cssText = "width: 80px; text-align: center; margin-bottom: 5px; display: inline-block;";
          badgeSpan.textContent = job.status.toUpperCase();

          const nameSpan = document.createElement("span");
          nameSpan.className = "text-primary-blue";
          nameSpan.textContent = job.name;

          jobNameDiv.appendChild(badgeSpan);
          jobNameDiv.appendChild(nameSpan);
          nameCell.appendChild(jobNameDiv);

          const dateCell = document.createElement("div");
          dateCell.className = "text-gray-400";
          dateCell.textContent = job.date;

          const feesCell = document.createElement("div");
          feesCell.className = "text-right text-primary-pink";
          feesCell.textContent = `($${job.fees.toFixed(2)})`;

          const billedCell = document.createElement("div");
          billedCell.className = "text-right font-medium";
          billedCell.innerHTML = `
            $${job.billed.toFixed(2)}
            <svg class="icon inline h-4 w-4 ml-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
          `;

          jobItem.appendChild(nameCell);
          jobItem.appendChild(dateCell);
          jobItem.appendChild(feesCell);
          jobItem.appendChild(billedCell);

          jobList.appendChild(jobItem);
        });
      } else {
        const noJobs = document.createElement("div");
        noJobs.className = "p-8 text-center text-gray-400";
        noJobs.textContent = "No transactions found matching your filters.";
        jobList.appendChild(noJobs);
      }

      // Show/hide load more button
      if (!state.dashboard.showAllTransactions && allJobs.length > 2) {
        loadMoreContainer.style.display = "block";
      } else {
        loadMoreContainer.style.display = "none";
      }
    }

    function updateTransactionHistory() {
      // Calculate available balance
      const availableBalanceValue = transactions.reduce((sum, tx) => sum + tx.amount, 0);
      const pendingAmountValue = 0.0;

      // Update summary
      availableBalance.innerHTML = `
        +$${availableBalanceValue.toFixed(2)}
        <svg class="icon inline h-4 w-4 ml-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <polyline points="9 18 15 12 9 6"></polyline>
        </svg>
      `;
      pendingAmount.textContent = `$${pendingAmountValue.toFixed(2)} pending`;

      // Update transaction list
      transactionList.innerHTML = "";
      transactions.forEach(tx => {
        const txItem = document.createElement("div");
        txItem.className = "grid grid-cols-5 gap-4 py-4 border-t border-gray-800";

        const dateCell = document.createElement("div");
        dateCell.textContent = tx.date;

        const typeCell = document.createElement("div");
        typeCell.textContent = tx.type;

        const contractCell = document.createElement("div");
        contractCell.innerHTML = `
          <div>${tx.contract}</div>
          ${tx.details ? `<div class="text-gray-400 text-sm">${tx.details}</div>` : ""}
        `;

        const clientCell = document.createElement("div");
        clientCell.className = "flex items-center";
        if (tx.clientInitials) {
          clientCell.innerHTML = `
            <div class="w-6 h-6 rounded-full bg-gray-700 flex items-center justify-center text-xs mr-2">
              ${tx.clientInitials}
            </div>
            ${tx.client}
          `;
        } else {
          clientCell.textContent = tx.client;
        }

        const amountCell = document.createElement("div");
        amountCell.className = `text-right ${tx.amount > 0 ? "text-primary-pink" : ""}`;
        amountCell.innerHTML = `
          ${tx.amount > 0 ? "+" : ""}$${tx.amount.toFixed(2)}
          <svg class="icon inline h-4 w-4 ml-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="9 18 15 12 9 6"></polyline>
          </svg>
        `;

        txItem.appendChild(dateCell);
        txItem.appendChild(typeCell);
        txItem.appendChild(contractCell);
        txItem.appendChild(clientCell);
        txItem.appendChild(amountCell);

        transactionList.appendChild(txItem);
      });
    }

    function updateLifetimeTab() {
      // For the lifetime tab, we'll combine all jobs from all clients
      const allJobs = [
        ...mariaSantosJobs,
        ...globalTechJobs,
        ...carlosJobs,
        ...elenaJobs
      ];

      // Calculate totals
      const totalBilledValue = allJobs.reduce((sum, job) => sum + job.billed, 0);
      const totalFeesValue = allJobs.reduce((sum, job) => sum + job.fees, 0);
      const netEarningsValue = totalBilledValue - totalFeesValue;

      // Update summary
      lifetimeEarnings.textContent = `$${netEarningsValue.toFixed(2)}`;
      lifetimeTotalBilled.textContent = `$${totalBilledValue.toFixed(2)}`;
      lifetimeTotalFees.textContent = `($${totalFeesValue.toFixed(2)})`;

      // Group jobs by client
      const clientData = {};
      allJobs.forEach(job => {
        if (!clientData[job.client]) {
          clientData[job.client] = {
            name: job.client,
            jobs: [],
            totalBilled: 0,
            totalFees: 0,
            netEarnings: 0
          };
        }
        clientData[job.client].jobs.push(job);
        clientData[job.client].totalBilled += job.billed;
        clientData[job.client].totalFees += job.fees;
        clientData[job.client].netEarnings = clientData[job.client].totalBilled - clientData[job.client].totalFees;
      });

      // Convert to array for sorting
      let clientsArray = Object.values(clientData);

      // Sort clients based on the selected sort order
      switch (state.lifetime.sortOrder) {
        case "alphabetical":
          clientsArray.sort((a, b) => a.name.localeCompare(b.name));
          break;
        case "highest":
          clientsArray.sort((a, b) => b.netEarnings - a.netEarnings);
          break;
        case "lowest":
          clientsArray.sort((a, b) => a.netEarnings - b.netEarnings);
          break;
      }

      // Update client list
      lifetimeClientList.innerHTML = "";
      if (clientsArray.length > 0) {
        clientsArray.forEach(client => {
          const clientItem = document.createElement("div");
          clientItem.className = "p-4 border-b border-gray-800 cursor-pointer";
          clientItem.style.transition = "background-color 0.2s ease";
          clientItem.addEventListener("mouseover", () => {
            clientItem.style.backgroundColor = "rgba(0, 58, 140, 0.1)";
          });
          clientItem.addEventListener("mouseout", () => {
            clientItem.style.backgroundColor = "";
          });

          // Get client initials
          const initials = client.name.split(' ')
            .map(word => word[0])
            .join('')
            .toUpperCase();

          // Show client name in the Lifetime Billed tab
          clientItem.innerHTML = `
            <div class="flex items-center">
              <div class="w-10 h-10 rounded-full bg-primary-blue flex items-center justify-center text-white font-bold mr-3">
                ${initials}
              </div>
              <div class="flex-1">
                <div class="font-medium text-primary-blue">${client.name}</div>
                <div class="text-sm text-gray-400">Total Earnings: $${client.netEarnings.toFixed(2)}</div>
              </div>
              <div>
                <svg class="icon h-5 w-5 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polyline points="9 18 15 12 9 6"></polyline>
                </svg>
              </div>
            </div>
          `;

          // Add click event to show client details
          clientItem.addEventListener("click", () => {
            state.clientDetail.client = client.name;
            state.dashboard.activeTab = "client-detail";
            updateClientDetailTab();

            // Hide all tab contents
            tabContents.forEach(content => {
              content.classList.add("hidden");
            });

            // Show client detail tab
            clientDetailTab.classList.remove("hidden");
          });

          lifetimeClientList.appendChild(clientItem);
        });
      } else {
        const noClients = document.createElement("div");
        noClients.className = "p-8 text-center text-gray-400";
        noClients.textContent = "No clients found for the selected period.";
        lifetimeClientList.appendChild(noClients);
      }
    }

    function updateClientDetailTab() {
      const clientName = state.clientDetail.client;
      let clientJobs = [];

      // Get all jobs for the selected client
      switch (clientName) {
        case "Maria Santos":
          clientJobs = mariaSantosJobs;
          break;
        case "Global Tech Solutions":
          clientJobs = globalTechJobs;
          break;
        case "Carlos Rodriguez":
          clientJobs = carlosJobs;
          break;
        case "Elena Fernandez":
          clientJobs = elenaJobs;
          break;
      }

      // Sort jobs based on the selected sort order
      const sortedJobs = clientJobs.sort((a, b) => {
        switch (state.clientDetail.sortOrder) {
          case "newest":
            return new Date(b.date).getTime() - new Date(a.date).getTime();
          case "oldest":
            return new Date(a.date).getTime() - new Date(b.date).getTime();
          case "highest":
            return b.billed - a.billed;
          case "lowest":
            return a.billed - b.billed;
          default:
            return 0;
        }
      });

      // Calculate totals
      const totalBilledValue = sortedJobs.reduce((sum, job) => sum + job.billed, 0);
      const totalFeesValue = sortedJobs.reduce((sum, job) => sum + job.fees, 0);
      const netEarningsValue = totalBilledValue - totalFeesValue;

      // Get client initials
      const initials = clientName.split(' ')
        .map(word => word[0])
        .join('')
        .toUpperCase();

      // Update client details
      clientNameHeader.textContent = clientName;
      clientInitials.textContent = initials;
      clientTotalBilled.textContent = `$${totalBilledValue.toFixed(2)}`;
      clientTotalFees.textContent = `($${totalFeesValue.toFixed(2)})`;
      clientNetEarnings.textContent = `$${netEarningsValue.toFixed(2)}`;

      // Update job list
      clientJobList.innerHTML = "";
      if (sortedJobs.length > 0) {
        sortedJobs.forEach(job => {
          const jobItem = document.createElement("div");
          jobItem.className = "grid grid-cols-4 gap-4 p-4 border-b border-gray-800";
          jobItem.style.transition = "background-color 0.2s ease";
          jobItem.addEventListener("mouseover", () => {
            jobItem.style.backgroundColor = "rgba(0, 58, 140, 0.1)";
          });
          jobItem.addEventListener("mouseout", () => {
            jobItem.style.backgroundColor = "";
          });

          const nameCell = document.createElement("div");
          nameCell.className = "font-medium flex items-center";
          const jobNameDiv = document.createElement("div");
          jobNameDiv.className = "flex flex-col";

          const badgeSpan = document.createElement("span");
          badgeSpan.className = `badge ${job.status === "paid" ? "badge-paid" : job.status === "pending" ? "badge-pending" : "badge-overdue"}`;
          badgeSpan.style.cssText = "width: 80px; text-align: center; margin-bottom: 5px; display: inline-block;";
          badgeSpan.textContent = job.status.toUpperCase();

          const nameSpan = document.createElement("span");
          nameSpan.className = "text-primary-blue"; // Make job name blue to match Billings & Earnings format
          nameSpan.textContent = job.name;

          jobNameDiv.appendChild(badgeSpan);
          jobNameDiv.appendChild(nameSpan);
          nameCell.appendChild(jobNameDiv);

          const dateCell = document.createElement("div");
          dateCell.className = "text-gray-400";
          dateCell.textContent = job.date;

          const feesCell = document.createElement("div");
          feesCell.className = "text-right text-primary-pink";
          feesCell.textContent = `($${job.fees.toFixed(2)})`;

          const billedCell = document.createElement("div");
          billedCell.className = "text-right font-medium";
          billedCell.innerHTML = `
            $${job.billed.toFixed(2)}
            <svg class="icon inline h-4 w-4 ml-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
          `;

          jobItem.appendChild(nameCell);
          jobItem.appendChild(dateCell);
          jobItem.appendChild(feesCell);
          jobItem.appendChild(billedCell);

          clientJobList.appendChild(jobItem);
        });
      } else {
        const noJobs = document.createElement("div");
        noJobs.className = "p-8 text-center text-gray-400";
        noJobs.textContent = "No transactions found for this client.";
        clientJobList.appendChild(noJobs);
      }
    }

    function updateView() {
      if (state.view === "dashboard") {
        dashboardView.style.display = "block";
        transactionHistoryView.style.display = "none";
        updateDashboard();

        // If lifetime tab is active, update it
        if (state.dashboard.activeTab === "lifetime") {
          updateLifetimeTab();
        }
      } else {
        dashboardView.style.display = "none";
        transactionHistoryView.style.display = "block";
        updateTransactionHistory();
      }
    }

    // Event Listeners
    transactionHistoryLink.addEventListener("click", (e) => {
      e.preventDefault();
      state.view = "transaction-history";
      updateView();
    });

    backToDashboardButton.addEventListener("click", () => {
      state.view = "dashboard";
      updateView();
    });

    tabs.forEach(tab => {
      tab.addEventListener("click", () => {
        const tabValue = tab.getAttribute("data-tab");
        state.dashboard.activeTab = tabValue;

        tabs.forEach(t => t.classList.remove("active"));
        tab.classList.add("active");

        tabContents.forEach(content => {
          if (content.id === `${tabValue}-tab`) {
            content.classList.remove("hidden");
          } else {
            content.classList.add("hidden");
          }
        });

        // If switching to lifetime tab, update its content
        if (tabValue === "lifetime") {
          updateLifetimeTab();
        }
      });
    });

    loadMoreButton.addEventListener("click", () => {
      state.dashboard.showAllTransactions = true;
      updateDashboard();
    });

    searchInput.addEventListener("input", (e) => {
      state.dashboard.searchQuery = e.target.value;
      updateDashboard();
    });

    refreshButton.addEventListener("click", () => {
      alert("Refreshing data...");
      updateDashboard();
    });

    downloadCsvButton.addEventListener("click", () => {
      alert("Downloading CSV file...");
    });

    // Dropdown handlers
    function toggleDropdown(dropdown) {
      dropdown.classList.toggle("show");
    }

    function closeAllDropdowns() {
      document.querySelectorAll(".dropdown-content, .select-content").forEach(dropdown => {
        dropdown.classList.remove("show");
      });
    }

    document.addEventListener("click", (e) => {
      if (!e.target.closest(".dropdown") && !e.target.closest(".select")) {
        closeAllDropdowns();
      }
    });

    // Status filter dropdown
    statusFilterButton.addEventListener("click", () => {
      toggleDropdown(statusFilterDropdown);
    });

    statusFilterDropdown.querySelectorAll(".dropdown-item").forEach(item => {
      item.addEventListener("click", () => {
        const value = item.getAttribute("data-value");
        state.dashboard.statusFilter = value;
        statusFilterText.textContent = item.textContent;
        closeAllDropdowns();
        updateDashboard();
      });
    });

    // Date range dropdowns
    dateRangeButton.addEventListener("click", () => {
      toggleDropdown(dateRangeDropdown);
    });

    dateRangeDropdown.querySelectorAll(".dropdown-item").forEach(item => {
      item.addEventListener("click", () => {
        const value = item.getAttribute("data-value");
        state.dashboard.dateRange = value;
        dateRangeText.textContent = value;
        dateRangeText2.textContent = value;
        closeAllDropdowns();
      });
    });

    dateRangeButton2.addEventListener("click", () => {
      toggleDropdown(dateRangeDropdown2);
    });

    dateRangeDropdown2.querySelectorAll(".dropdown-item").forEach(item => {
      item.addEventListener("click", () => {
        const value = item.getAttribute("data-value");
        state.dashboard.dateRange = value;
        dateRangeText.textContent = value;
        dateRangeText2.textContent = value;
        closeAllDropdowns();
      });
    });

    // Sort dropdown
    sortTrigger.addEventListener("click", () => {
      toggleDropdown(sortDropdown);
    });

    sortDropdown.querySelectorAll(".select-item").forEach(item => {
      item.addEventListener("click", () => {
        const value = item.getAttribute("data-value");
        state.dashboard.sortOrder = value;
        sortValue.textContent = item.textContent;
        closeAllDropdowns();
        updateDashboard();
      });
    });

    // Transaction history dropdowns
    transactionTypeTrigger.addEventListener("click", () => {
      toggleDropdown(transactionTypeDropdown);
    });

    transactionTypeDropdown.querySelectorAll(".select-item").forEach(item => {
      item.addEventListener("click", () => {
        const value = item.getAttribute("data-value");
        state.transactionHistory.transactionType = value;
        transactionTypeValue.textContent = value;
        closeAllDropdowns();
      });
    });

    clientFilterTrigger.addEventListener("click", () => {
      toggleDropdown(clientFilterDropdown);
    });

    clientFilterDropdown.querySelectorAll(".select-item").forEach(item => {
      item.addEventListener("click", () => {
        const value = item.getAttribute("data-value");
        state.transactionHistory.clientFilter = value;
        clientFilterValue.textContent = value;
        closeAllDropdowns();
      });
    });

    contractFilterTrigger.addEventListener("click", () => {
      toggleDropdown(contractFilterDropdown);
    });

    contractFilterDropdown.querySelectorAll(".select-item").forEach(item => {
      item.addEventListener("click", () => {
        const value = item.getAttribute("data-value");
        state.transactionHistory.contractFilter = value;
        contractFilterValue.textContent = value;
        closeAllDropdowns();
      });
    });

    newDesignSwitch.addEventListener("change", () => {
      state.transactionHistory.newDesign = newDesignSwitch.checked;
    });

    // Lifetime tab event listeners
    lifetimeDateRangeButton.addEventListener("click", () => {
      toggleDropdown(lifetimeDateRangeDropdown);
    });

    lifetimeDateRangeDropdown.querySelectorAll(".dropdown-item").forEach(item => {
      item.addEventListener("click", () => {
        const value = item.getAttribute("data-value");
        state.lifetime.dateRange = value;
        lifetimeDateRangeText.textContent = value;
        closeAllDropdowns();
        updateLifetimeTab();
      });
    });

    lifetimeSortTrigger.addEventListener("click", () => {
      toggleDropdown(lifetimeSortDropdown);
    });

    lifetimeSortDropdown.querySelectorAll(".select-item").forEach(item => {
      item.addEventListener("click", () => {
        const value = item.getAttribute("data-value");
        state.lifetime.sortOrder = value;
        lifetimeSortValue.textContent = item.textContent;
        closeAllDropdowns();
        updateLifetimeTab();
      });
    });

    lifetimeDownloadCsvButton.addEventListener("click", () => {
      alert("Downloading lifetime earnings CSV file...");
    });

    // Client detail tab event listeners
    backToLifetimeButton.addEventListener("click", () => {
      state.dashboard.activeTab = "lifetime";

      // Hide all tab contents
      tabContents.forEach(content => {
        content.classList.add("hidden");
      });

      // Show lifetime tab
      document.getElementById("lifetime-tab").classList.remove("hidden");
    });

    clientSortTrigger.addEventListener("click", () => {
      toggleDropdown(clientSortDropdown);
    });

    clientSortDropdown.querySelectorAll(".select-item").forEach(item => {
      item.addEventListener("click", () => {
        const value = item.getAttribute("data-value");
        state.clientDetail.sortOrder = value;
        clientSortValue.textContent = item.textContent;
        closeAllDropdowns();
        updateClientDetailTab();
      });
    });

    clientDownloadCsvButton.addEventListener("click", () => {
      alert(`Downloading ${state.clientDetail.client} transactions CSV file...`);
    });

    // Initialize
    updateView();

    // Mobile Menu Toggle
    document.addEventListener('DOMContentLoaded', function() {
      const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
      const mobileMenu = document.getElementById('mobileMenu');

      if (mobileMenuBtn && mobileMenu) {
        mobileMenuBtn.addEventListener('click', () => {
          mobileMenu.classList.toggle('active');
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', (event) => {
          if (!mobileMenuBtn.contains(event.target) && !mobileMenu.contains(event.target)) {
            mobileMenu.classList.remove('active');
          }
        });
      }

      // Search type dropdown
      const searchTypeBtn = document.getElementById('searchTypeBtn');
      const searchTypeDropdown = document.getElementById('searchTypeDropdown');
      const selectedSearchType = document.getElementById('selectedSearchType');
      const searchInput = document.getElementById('searchInput');

      if (searchTypeBtn && searchTypeDropdown) {
        // Toggle dropdown
        searchTypeBtn.addEventListener('click', function() {
          searchTypeDropdown.classList.toggle('active');
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
          if (!searchTypeBtn.contains(e.target) && !searchTypeDropdown.contains(e.target)) {
            searchTypeDropdown.classList.remove('active');
          }
        });
      }

      // Close dropdowns when clicking outside
      window.addEventListener('click', function(e) {
        if (!e.target.matches('.nav-dropbtn')) {
          const dropdowns = document.getElementsByClassName('nav-dropdown-content');
          for (let dropdown of dropdowns) {
            if (dropdown.classList.contains('show')) {
              dropdown.classList.remove('show');
            }
          }
        }
      });

      // Profile dropdown
      const profileDropdown = document.querySelector('.profile-dropdown');
      if (profileDropdown) {
        const profileButton = profileDropdown.querySelector('.profile-button');

        // Toggle dropdown on profile button click
        if (profileButton) {
          profileButton.addEventListener('click', function(e) {
            e.stopPropagation();
            profileDropdown.classList.toggle('active');
          });
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
          if (!profileDropdown.contains(e.target)) {
            profileDropdown.classList.remove('active');
          }
        });
      }

      // Handle window resize for responsive behavior
      window.addEventListener('resize', () => {
        if (window.innerWidth > 768) {
          // Hide mobile menu on desktop
          if (mobileMenu) {
            mobileMenu.classList.remove('active');
          }
        }
      });
    });
  </script>
  </div>
  </div>

  <!-- Footer -->
  <footer>
    <div class="container">
        <div class="footer-content">
            <div class="footer-section">
                <h3>For Clients</h3>
                <a href="{{ url_for('how_to_hire') }}">How to Hire</a>
                <a href="{{ url_for('marketplace') }}">Marketplace</a>
                <a href="{{ url_for('payroll_services') }}">Payroll Services</a>
                <a href="{{ url_for('service_catalog') }}">Service Catalog</a>
                <a href="{{ url_for('business_networking') }}">Business Networking</a>
                <a href="{{ url_for('ph_business_loan') }}">PH Business Loan</a>
            </div>
            <div class="footer-section">
                <h3>For Geniuses</h3>
                <a href="{{ url_for('how_it_works') }}">How It Works?</a>
                <a href="{{ url_for('why_cant_apply') }}">Why Can't I Apply?</a>
                <a href="{{ url_for('direct_contracts') }}">Direct Contracts</a>
                <a href="{{ url_for('find_mentors') }}">Find Mentors</a>
                <a href="{{ url_for('mentor_application') }}">Mentor Application</a>
                <a href="{{ url_for('ph_health_insurance') }}">PH Health Insurance</a>
                <a href="{{ url_for('ph_life_insurance') }}">PH Life Insurance</a>
            </div>
            <div class="footer-section">
                <h3>Resources</h3>
                <a href="{{ url_for('help_and_support') }}">Help & Support</a>
                <a href="{{ url_for('news_and_events') }}">News & Events</a>
                <a href="{{ url_for('affiliate_program') }}">Affiliate Program</a>
            </div>
            <div class="footer-section">
                <h3>Company</h3>
                <a href="{{ url_for('about_us') }}">About Us</a>
                <a href="{{ url_for('contact_us') }}">Contact Us</a>
                <a href="{{ url_for('charity_projects') }}">Charity Projects</a>
            </div>
        </div>
        <div class="footer-bottom">
            <div class="social-links-container">
                <span>Follow Us:</span>
                <div class="social-links">
                    <a href="https://www.facebook.com/giggenius.io" aria-label="Facebook"><i class="bi bi-facebook"></i></a>
                    <a href="https://www.instagram.com/giggenius.io/" aria-label="Instagram"><i class="bi bi-instagram"></i></a>
                    <a href="https://twitter.com/giggenius_io" aria-label="Twitter"><i class="bi bi-twitter-x"></i></a>
                    <a href="https://www.tiktok.com/@giggenius.io" aria-label="TikTok"><i class="bi bi-tiktok"></i></a>
                    <a href="https://www.youtube.com/@giggenius" aria-label="YouTube"><i class="bi bi-youtube"></i></a>
                    <a href="https://www.linkedin.com/company/gig-genius/" aria-label="LinkedIn"><i class="bi bi-linkedin"></i></a>
                </div>
            </div>
            <p>©2025 GigGenius by <a href="https://genuinelysolutions.com/">Genuinely Business Solutions</a></p>
            <div class="footer-links">
                <a href="{{ url_for('terms_of_service') }}">Terms of Service</a>
                <a href="{{ url_for('privacy_policy') }}">Privacy Policy</a>
            </div>
        </div>
    </div>
  </footer>
</body>
</html>
