<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Withdraw Earnings - GigGenius</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #004AAD;
            --primary-pink: #CD208B;
            --yellow: #FFD700;
            --text-dark: #000000;
            --text-light: #FFFFFF;
            --text-gray: #666;
            --text-muted: #6b7280;
            --bg-light: #f8f9fa;
            --bg-white: #ffffff;
            --border-color: #e5e7eb;
            --success-color: #4caf50;
            --warning-color: #ff9800;
            --inactive-color: #666666;
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --radius-sm: 0.25rem;
            --radius-md: 0.375rem;
            --radius-lg: 0.5rem;
            --radius-full: 9999px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        body {
            background-color: var(--bg-light);
            color: var(--text-dark);
            line-height: 1.5;
            min-height: 100vh;
        }

        a {
            text-decoration: none;
            color: inherit;
        }

        /* Layout */
        .container {
            max-width: 2000px;
            margin: 0 auto;
            background-color: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .main-content {
            padding: 1.5rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .dashboard {
            display: flex;
            width: 100%;
            background-color: var(--bg-white);
            color: var(--text-dark);
        }

        /* Navbar Styles */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: 5rem;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        .logo {
            display: flex;
            align-items: center;
            text-decoration: none;
        }

        .logo img {
            width: 3.5rem;
            height: 3.5rem;
            border-radius: 50%;
            border: 2px solid var(--primary-blue);
            object-fit: cover;
        }

        .logo h1 {
            font-size: 1.7rem;
            font-weight: bold;
            color: var(--primary-pink);
            margin-left: 0.5rem;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }
        }

        .nav-links a {
            color: var(--primary-blue);
            text-decoration: none;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .nav-links a:hover {
            color: var(--primary-pink);
        }

        .nav-dropdown {
            position: relative;
            display: inline-block;
        }

        .nav-dropbtn {
            font-weight: 500;
            font-size: 1.1rem;
            color: var(--primary-blue);
            background: none;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0;
        }

        .nav-dropbtn:hover {
            color: var(--primary-pink);
        }

        .nav-dropdown-content {
            display: none;
            position: absolute;
            background-color: #fff;
            min-width: 200px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            z-index: 1001;
            top: 100%;
            left: 0;
        }

        .nav-dropdown-content a {
            color: var(--primary-blue);
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            font-size: 1rem;
        }

        .nav-dropdown-content a:hover {
            background-color: #f9f9f9;
            color: var(--primary-pink);
        }

        .nav-dropdown:hover .nav-dropdown-content {
            display: block;
        }

        /* Right section container */
        .right-section {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        /* Search container */
        .search-container {
            display: flex;
            align-items: center;
        }

        @media (max-width: 768px) {
            .search-container {
                display: none;
            }
        }

        .search-type-select {
            position: relative;
        }

        .search-type-button {
            height: 2.5rem;
            background: white;
            border: 1px solid var(--primary-blue);
            border-right: none;
            border-radius: 8px 0 0 8px;
            padding: 0 1rem;
            color: var(--primary-blue);
            font-size: 1rem;
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .search-bar {
            height: 2.5rem;
            display: flex;
            align-items: center;
            background: white;
            border: 1px solid var(--primary-blue);
            border-radius: 0 8px 8px 0;
            width: 200px;
        }

        .search-bar input {
            border: none;
            outline: none;
            padding: 0 0.5rem;
            width: 100%;
            height: 100%;
            font-size: 1rem;
        }

        .search-bar .icon {
            color: var(--primary-blue);
            padding: 0 0.5rem;
        }

        /* Auth buttons container */
        .auth-buttons {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        /* Notification icon */
        .notification-icon {
            position: relative;
            cursor: pointer;
        }

        .notification-icon i {
            font-size: 1.5rem;
            color: var(--primary-blue);
        }

        .notification-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: var(--primary-pink);
            color: white;
            border-radius: 50%;
            padding: 0.1rem 0.4rem;
            font-size: 0.8rem;
            min-width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Profile button */
        .profile-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            cursor: pointer;
            border: 2px solid var(--primary-blue);
        }

        .profile-button img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* Profile dropdown */
        .profile-dropdown {
            position: relative;
            display: inline-block;
        }

        .profile-dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            top: 50px;
            background-color: #fff;
            min-width: 200px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            z-index: 1001;
        }

        .profile-dropdown-content a {
            color: var(--primary-blue);
            padding: 12px 16px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1rem;
        }

        .profile-dropdown-content a i {
            width: 20px;
            text-align: center;
        }

        .profile-dropdown-content a:hover {
            background-color: #f9f9f9;
            color: var(--primary-pink);
        }

        .dropdown-divider {
            height: 1px;
            background-color: #eee;
            margin: 8px 0;
        }

        .logout-option {
            color: #dc3545 !important;
        }

        .logout-option:hover {
            background-color: #fff5f5 !important;
            color: #dc3545 !important;
        }

        /* Show dropdown on hover */
        .profile-dropdown:hover .profile-dropdown-content {
            display: block;
        }

        /* Mobile menu styles */
        .mobile-menu-btn {
            background: none;
            border: none;
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 6px;
            display: none;
            align-items: center;
            justify-content: center;
        }

        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: flex;
            }
        }

        .mobile-menu-btn:hover {
            background-color: #f3f4f6;
        }

        .mobile-menu {
            display: none;
            position: absolute;
            top: 60px;
            right: 1rem;
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            width: 200px;
            z-index: 20;
            border: 1px solid #e5e7eb;
        }

        .mobile-menu.active {
            display: block;
        }

        .mobile-menu a {
            display: block;
            padding: 0.75rem 1rem;
            color: #333;
            font-size: 0.875rem;
            border-bottom: 1px solid #f3f4f6;
        }

        .mobile-menu a:last-child {
            border-bottom: none;
        }

        .mobile-menu a:hover {
            background-color: #f9fafb;
        }

        /* Rest of your existing styles remain unchanged */
        /* Sidebar styles */
        .sidebar {
            width: 250px;
            background-color: white;
            padding: 30px 0;
            flex-shrink: 0;
            border-right: 1px solid var(--border-color);
        }

        .sidebar h1 {
            font-size: 28px;
            margin-bottom: 30px;
            padding: 0 20px;
        }

        .sidebar-category {
            margin-bottom: 20px;
        }

        .sidebar-category-title {
            font-size: 16px;
            font-weight: 600;
            padding: 10px 20px;
            color: var(--text-dark);
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            padding: 8px 20px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .sidebar-menu li a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
        }

        .sidebar-menu li:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        .sidebar-menu li.active {
            border-left: 3px solid var(--primary-pink);
            background-color: rgba(0, 0, 0, 0.03);
        }

        .main-content {
            flex-grow: 1;
            padding: 30px;
            overflow-y: auto;
        }

        .section-title {
            font-size: 24px;
            margin-bottom: 20px;
        }

        .card {
            background-color: var(--bg-white);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-sm);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
        }

        .card-edit {
            background-color: transparent;
            border: none;
            color: var(--primary-pink);
            cursor: pointer;
            display: flex;
            align-items: center;
            font-size: 14px;
        }

        .card-edit svg {
            width: 16px;
            height: 16px;
            margin-right: 5px;
        }

        .balance {
            font-size: 32px;
            font-weight: 700;
            color: var(--success-color);
            margin: 10px 0;
        }

        .pending {
            font-size: 14px;
            color: var(--warning-color);
            margin-bottom: 15px;
        }

        .btn {
            padding: 10px 16px;
            border-radius: 4px;
            font-weight: 500;
            cursor: pointer;
            font-size: 14px;
            border: none;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background-color: var(--primary-pink);
            color: white;
        }

        .btn-secondary {
            background-color: transparent;
            color: var(--primary-pink);
            border: 1px solid var(--primary-pink);
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }

        .info-row {
            display: flex;
            margin-bottom: 15px;
        }

        .info-label {
            width: 120px;
            font-size: 14px;
            color: var(--inactive-color);
        }

        .info-value {
            flex-grow: 1;
            font-size: 14px;
        }

        .divider {
            height: 1px;
            background-color: var(--border-color);
            margin: 20px 0;
        }

        .withdrawal-method {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .method-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--primary-blue);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
            font-weight: bold;
        }

        .method-details {
            flex-grow: 1;
        }

        .method-name {
            font-size: 14px;
            margin-bottom: 5px;
        }

        .method-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            background-color: rgba(255, 255, 255, 0.1);
            font-size: 12px;
            margin-left: 10px;
        }

        .method-actions {
            display: flex;
            gap: 10px;
        }

        .action-link {
            color: var(--primary-pink);
            text-decoration: none;
            font-size: 14px;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 22px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--inactive-color);
            transition: .4s;
            border-radius: 34px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .toggle-slider {
            background-color: var(--primary-pink);
        }

        input:checked + .toggle-slider:before {
            transform: translateX(20px);
        }

        .verification-step {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .step-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: var(--success-color);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
            font-size: 12px;
        }

        .step-details {
            flex-grow: 1;
        }

        .step-title {
            font-size: 14px;
            margin-bottom: 5px;
        }

        .step-description {
            font-size: 12px;
            color: var(--inactive-color);
        }

        .tab-navigation {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 20px;
        }

        .tab {
            padding: 10px 20px;
            cursor: pointer;
            font-size: 14px;
            border-bottom: 2px solid transparent;
        }

        .tab.active {
            border-bottom: 2px solid var(--primary-pink);
            color: var(--primary-pink);
        }

        .dropdown {
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 10px 15px;
            font-size: 14px;
            color: var(--text-color);
            width: 100%;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .dropdown-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }

        .dropdown-row .dropdown {
            flex-grow: 1;
        }

        .checkbox-row {
            display: flex;
            align-items: center;
            margin-top: 10px;
        }

        .checkbox-row input[type="checkbox"] {
            margin-right: 10px;
        }

        .checkbox-row label {
            font-size: 14px;
        }

        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px 0;
        }

        .empty-state-icon {
            width: 80px;
            height: 80px;
            margin-bottom: 20px;
            color: var(--inactive-color);
        }

        .empty-state-text {
            font-size: 14px;
            color: var(--inactive-color);
        }

        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
        }


        .security-option {
            margin-bottom: 20px;
        }

        .security-option-title {
            font-size: 16px;
            margin-bottom: 10px;
        }

        .security-option-description {
            font-size: 14px;
            color: var(--inactive-color);
            margin-bottom: 10px;
        }

        /* Profile Settings Styles */
        .profile-section {
            background-color: white;
            color: #333;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-sm);
        }

        .profile-section-title {
            font-size: 24px;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .profile-view-link {
            color: var(--primary-blue);
            text-decoration: none;
            font-size: 14px;
            display: block;
            margin-bottom: 20px;
        }

        .profile-view-link:hover {
            text-decoration: underline;
        }

        .profile-field {
            margin-bottom: 20px;
        }

        .profile-field-label {
            font-size: 16px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .profile-field-edit {
            background-color: #003a8c;
            color: white;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .profile-dropdown {
            background-color: white;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 12px 15px;
            font-size: 16px;
            color: var(--text-dark);
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
        }

        .profile-radio-group {
            display: flex;
            gap: 20px;
        }

        .profile-radio-option {
            background-color: white;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 15px;
            flex: 1;
            position: relative;
            cursor: pointer;
        }

        .profile-radio-option.selected {
            border-color: var(--primary-blue);
        }

        .profile-radio-option .radio-circle {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid #666;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .profile-radio-option.selected .radio-circle {
            border-color: var(--primary-blue);
        }

        .profile-radio-option.selected .radio-circle::after {
            content: "";
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: var(--primary-blue);
        }

        .profile-radio-title {
            font-size: 16px;
            margin-bottom: 5px;
        }

        .profile-radio-description {
            font-size: 14px;
            color: var(--inactive-color);
        }

        .profile-checkbox {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 15px;
        }

        .profile-checkbox input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: var(--primary-blue);
        }

        .profile-checkbox label {
            font-size: 14px;
        }

        .profile-help-text {
            font-size: 14px;
            color: var(--inactive-color);
            margin-top: 5px;
        }

        .profile-category {
            margin-bottom: 30px;
        }

        .profile-category-title {
            font-size: 18px;
            margin-bottom: 15px;
        }

        .profile-badge {
            display: inline-block;
            padding: 5px 10px;
            background-color: #f5f5f5;
            border-radius: 20px;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
            color: var(--primary-blue);
        }

        .profile-badge-container {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }

        .profile-specialized {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .profile-specialized-title {
            font-size: 18px;
            font-weight: 600;
        }

        .profile-specialized-count {
            font-size: 14px;
            color: var(--inactive-color);
        }

        .profile-specialized-description {
            font-size: 14px;
            margin-bottom: 20px;
        }

        .profile-specialized-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-top: 1px solid var(--border-color);
        }

        .profile-specialized-item-title {
            font-size: 16px;
        }

        .profile-specialized-item-status {
            font-size: 14px;
            color: var(--success-color);
        }

        .profile-specialized-item-menu {
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        /* Profile URL edit styles */
        .profile-url-form {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .profile-url-prefix {
            background-color: #f5f5f5;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-right: none;
            border-radius: 4px 0 0 4px;
            color: var(--inactive-color);
            font-size: 14px;
        }

        .profile-url-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 0 4px 4px 0;
            font-size: 14px;
            outline: none;
        }

        .profile-url-input:focus {
            border-color: var(--primary-blue);
        }

        .profile-url-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        /* Project preference styles */
        .project-preference-option {
            display: flex;
            padding: 15px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: border-color 0.2s, box-shadow 0.2s;
        }

        .project-preference-option:hover {
            border-color: var(--primary-blue);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .project-preference-option.selected {
            border-color: var(--primary-blue);
            background-color: rgba(0, 58, 140, 0.05);
        }

        .project-preference-icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: var(--primary-blue);
            flex-shrink: 0;
        }

        .project-preference-content {
            flex: 1;
        }

        .project-preference-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 5px;
            color: var(--text-dark);
        }

        .project-preference-description {
            font-size: 14px;
            color: var(--inactive-color);
            margin-bottom: 10px;
        }

        .project-preference-features {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }

        .feature {
            font-size: 14px;
            display: flex;
            align-items: center;
        }

        .feature-icon {
            color: var(--primary-blue);
            margin-right: 5px;
            font-weight: bold;
        }

        /* Responsive styles */
        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                border-right: none;
                border-bottom: 1px solid var(--border-color);
                padding: 15px 0;
            }

            .sidebar h1 {
                font-size: 24px;
                margin-bottom: 15px;
            }

            .main-content {
                padding: 15px;
            }

            .header {
                padding: 0.5rem 1rem;
            }

            .nav-links {
                display: none;
            }

            .search-container {
                display: none;
            }
        }

        /* Footer Styles */
        footer {
            background-color: #1a1a1a;
            color: #fff;
            padding: 3rem 0;
            margin-top: 3rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section {
            display: flex;
            flex-direction: column;
        }

        .footer-section h3 {
            font-size: 1.25rem;
            margin-bottom: 1.5rem;
            color: #fff;
            font-weight: 600;
        }

        .footer-section a {
            color: #b3b3b3;
            margin-bottom: 0.75rem;
            transition: color 0.2s;
            text-decoration: none;
        }

        .footer-section a:hover {
            color: var(--primary-pink);
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid #333;
        }

        .footer-bottom p {
            margin: 1rem 0;
            color: #b3b3b3;
        }

        .footer-bottom a {
            color: #fff;
            transition: color 0.2s;
            text-decoration: none;
        }

        .footer-bottom a:hover {
            color: var(--primary-pink);
        }

        .social-links-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .social-links {
            display: flex;
            gap: 1rem;
        }

        .social-links a {
            font-size: 1.25rem;
            color: #fff;
            transition: color 0.2s;
        }

        .social-links a:hover {
            color: var(--primary-pink);
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 2rem;
        }

        @media (max-width: 768px) {
            .footer-links {
                flex-direction: column;
                gap: 0.5rem;
            }
        }

        @media (max-width: 480px) {
            .footer-content {
                grid-template-columns: 1fr;
            }
        }

        .social-icons a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            margin: 0;
        }

        .social-icons .bi {
            font-size: 1.2rem;
            color: var(--text-light);
            transition: transform 0.3s ease, color 0.3s ease;
        }

        .social-icons a:hover {
            background-color: var(--primary-pink);
            transform: translateY(-3px);
        }

        /* Add responsive styles for footer */
        @media (max-width: 768px) {
            .footer-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 2rem;
            }

            .footer-bottom {
                flex-direction: column;
                gap: 20px;
            }
        }

        @media (max-width: 480px) {
            .footer-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <nav class="navbar">
            <div class="navbar-left">
                <a href="{{ url_for('landing_page') }}" class="logo">
                    <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo">
                    <h1>GigGenius</h1>
                </a>
                <div class="nav-links">
                    <a href="{{ url_for('genius_page') }}">Find Gigs</a>
                    <a href="{{ url_for('my_proposal') }}">Proposals</a>
                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Contracts <i class="bi bi-chevron-down"></i></button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('tracker') }}">Log Works</a>
                        </div>
                    </div>
                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Earnings <i class="bi bi-chevron-down"></i></button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('billing_and_earnings') }}">Billings and Earnings</a>
                            <a href="{{ url_for('withdraw_earnings') }}">Withdraw Earnings</a>
                            <a href="{{ url_for('tax_info') }}">Tax Info</a>
                        </div>
                    </div>
                    <a href="{{ url_for('landing_page') }}">Messages</a>
                </div>
            </div>
            <div class="right-section">
                <div class="search-container">
                    <div class="search-type-select">
                        <button id="searchTypeBtn" class="search-type-button">
                            <span id="selectedSearchType">Gigs</span>
                            <i class="bi bi-chevron-down"></i>
                        </button>
                    </div>
                    <div class="search-bar">
                        <input type="text" id="searchInput" placeholder="Search for gigs...">
                        <div class="icon">
                            <i class="bi bi-search"></i>
                        </div>
                    </div>
                </div>
                <div class="auth-buttons">
                    <div class="notification-icon">
                        <i class="bi bi-bell"></i>
                        <div class="notification-badge">3</div>
                    </div>
                    <div class="profile-dropdown">
                        <div class="profile-button">
                            <img src="{{ genius.profile_picture_url if genius else url_for('static', filename='img/default_profile.png') }}" alt="Profile Picture">
                        </div>
                        <div class="profile-dropdown-content">
                            <a href="{{ url_for('genius_profile') }}"><i class="bi bi-person"></i> Your Profile</a>
                            <a href="#"><i class="bi bi-gear"></i> Settings</a>
                            <a href="#"><i class="bi bi-question-circle"></i> Help Center</a>
                            <div class="dropdown-divider"></div>
                            <a href="#" class="logout-option"><i class="bi bi-box-arrow-right"></i> Logout</a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <div class="mobile-menu" id="mobileMenu">
            <a href="{{ url_for('genius_page') }}">Find Gigs</a>
            <a href="{{ url_for('my_proposal') }}">Proposals</a>
            <a href="#">Contracts</a>
            <a href="{{ url_for('tracker') }}">Log Works</a>
            <a href="{{ url_for('billing_and_earnings') }}">Billings and Earnings</a>
            <a href="{{ url_for('withdraw_earnings') }}">Withdraw Earnings</a>
            <a href="{{ url_for('tax_info') }}">Tax Info</a>
            <a href="{{ url_for('landing_page') }}">Messages</a>
            <a href="{{ url_for('genius_profile') }}">Profile</a>
        </div>

        <div class="main-content">
            <div class="dashboard">
                <div class="sidebar">
                    <h1>Settings</h1>

                    <div class="sidebar-category">
                        <div class="sidebar-category-title">Billing</div>
                        <ul class="sidebar-menu">
                            <li data-section="billing">Billing & Payments</li>
                        </ul>
                    </div>

                    <div class="sidebar-category">
                        <div class="sidebar-category-title">User Settings</div>
                        <ul class="sidebar-menu">
                            <li data-section="contact">Contact Info</li>
                            <li data-section="profile"><a href="Genius_Profile.html">My Profile</a></li>
                            <li data-section="profile-settings">Profile Settings</li>
                            <li data-section="get-paid" class="active">Get Paid</li>
                            <li data-section="services">Connected Services</li>
                            <li data-section="security">Password & Security</li>
                            <li data-section="verification">Identity Verification</li>
                            <li data-section="notifications">Notification Settings</li>
                        </ul>
                    </div>
                </div>

                <div class="main-content">
                    <!-- Get Paid Section -->
                    <div id="get-paid" class="content-section active">
                        <h2 class="section-title">Get paid</h2>

                        <div class="card">
                            <div class="card-title">Available balance</div>
                            <div class="balance">$22.91</div>
                            <div class="pending">+$0.00 pending</div>
                            <button class="btn btn-primary">Get paid now</button>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <div class="card-title">Withdrawal schedule</div>
                                <button class="card-edit">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                                    </svg>
                                </button>
                            </div>

                            <div>Monthly (next on Apr 30)</div>
                            <div style="font-size: 14px; color: var(--inactive-color); margin: 10px 0;">Only when balance is $1,000.00 or more. <a href="#" class="action-link">View payment calendar.</a></div>

                            <div>Direct to Local Bank (PHP) - Account ending in 7691</div>
                        </div>

                        <div class="card">
                            <div class="card-title">Last withdrawal</div>
                            <div style="margin: 10px 0;">$75.51 to Direct to Local Bank (PHP) - Account ending in 7691</div>
                            <div style="font-size: 14px; color: var(--inactive-color); margin-bottom: 10px;">Aug 2, 2024</div>
                            <a href="#" class="action-link">View transaction history</a>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <div class="card-title">Withdrawal methods</div>
                                <button class="btn btn-secondary btn-small">Add a method</button>
                            </div>

                            <div style="font-size: 14px; margin: 10px 0;">To change which method is preferred, <a href="#" class="action-link">edit your withdrawal schedule</a>.</div>

                            <div class="withdrawal-method">
                                <div class="method-icon">D</div>
                                <div class="method-details">
                                    <div class="method-name">Direct to Local Bank (PHP) - Account ending in 7691 <span class="method-badge">Preferred</span></div>
                                </div>
                                <div class="method-actions">
                                    <a href="#" class="action-link">Edit</a>
                                    <a href="#" class="action-link">Remove</a>
                                </div>
                            </div>

                            <div class="withdrawal-method">
                                <div class="method-icon">D</div>
                                <div class="method-details">
                                    <div class="method-name">Direct to Local Bank (PHP) - Account ending in 3038</div>
                                </div>
                                <div class="method-actions">
                                    <a href="#" class="action-link">Edit</a>
                                    <a href="#" class="action-link">Remove</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Billing & Payments Section -->
                    <div id="billing" class="content-section">
                        <h2 class="section-title">Billing & payments</h2>

                        <div class="card">
                            <div class="card-title">Manage billing methods</div>
                            <div style="font-size: 14px; margin: 10px 0;">Add, update, or remove your billing methods.</div>

                            <div style="margin: 20px 0;">
                                <div style="font-weight: 600; margin-bottom: 10px;">Primary</div>
                                <div style="font-size: 14px; margin-bottom: 15px;">Your primary billing method is used for all recurring payments.</div>

                                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 15px;">
                                    <div style="display: flex; align-items: center;">
                                        <div style="width: 30px; height: 30px; background-color: var(--primary-blue); border-radius: 4px; margin-right: 10px; display: flex; align-items: center; justify-content: center; color: white;">P</div>
                                        <div>
                                            <div>PayPal</div>
                                            <div style="font-size: 12px; color: var(--inactive-color);"><EMAIL></div>
                                        </div>
                                    </div>
                                    <a href="#" class="action-link">Remove</a>
                                </div>

                                <div style="display: flex; align-items: center; color: var(--warning-color); font-size: 14px; margin-bottom: 15px;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-right: 5px;">
                                        <circle cx="12" cy="12" r="10"></circle>
                                        <line x1="12" y1="8" x2="12" y2="12"></line>
                                        <line x1="12" y1="16" x2="12.01" y2="16"></line>
                                    </svg>
                                    Recent charge failed.
                                </div>

                                <button class="btn btn-secondary" style="display: flex; align-items: center;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-right: 5px;">
                                        <line x1="12" y1="5" x2="12" y2="19"></line>
                                        <line x1="5" y1="12" x2="19" y2="12"></line>
                                    </svg>
                                    Add a billing method
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Info Section -->
                    <div id="contact" class="content-section">
                        <h2 class="section-title">Contact info</h2>

                        <div class="card">
                            <div class="card-header">
                                <div class="card-title">Account</div>
                                <button class="card-edit">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                                    </svg>
                                </button>
                            </div>

                            <div class="info-row">
                                <div class="info-label">User ID</div>
                                <div class="info-value">nashesguerra</div>
                            </div>

                            <div class="info-row">
                                <div class="info-label">Name</div>
                                <div class="info-value">Nash Esguerra</div>
                            </div>

                            <div class="info-row">
                                <div class="info-label">Email</div>
                                <div class="info-value"><EMAIL></div>
                            </div>

                            <div class="info-row">
                                <a href="#" class="action-link">Close my account</a>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <div class="card-title">Additional accounts</div>
                            </div>

                            <p style="font-size: 14px; margin-bottom: 15px;">Creating a new account allows you to use the platform in different ways, while still having just one login.</p>

                            <div class="info-row">
                                <div class="info-label">Client Account</div>
                                <div class="info-value">I hire, manage and pay as a different company. Each client company has its own freelancers, payment methods and reports.</div>
                            </div>

                            <button class="btn btn-secondary btn-small">New Client Account</button>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <div class="card-title">Location</div>
                                <button class="card-edit">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                                    </svg>
                                </button>
                            </div>

                            <div class="info-row">
                                <div class="info-label">Time Zone</div>
                                <div class="info-value">UTC+08:00 Hong Kong SAR, Perth, Singapore, Taipei</div>
                            </div>

                            <div class="info-row">
                                <div class="info-label">Address</div>
                                <div class="info-value">
                                    123 Main Street<br>
                                    Anytown, PH 1234<br>
                                    Philippines
                                </div>
                            </div>

                            <div class="info-row">
                                <div class="info-label">Phone</div>
                                <div class="info-value">+63 9203103689</div>
                            </div>
                        </div>
                    </div>

                    <!-- Profile Settings Section -->
                    <div id="profile-settings" class="content-section">
                        <h2 class="section-title">My profile</h2>

                        <div class="profile-section">
                            <a href="#" class="profile-view-link">View my profile as others see it</a>

                            <div class="profile-field">
                                <div class="profile-field-label">
                                    Visibility
                                </div>
                                <div class="profile-dropdown" id="visibility-dropdown">
                                    <span>Public</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <polyline points="6 9 12 15 18 9"></polyline>
                                    </svg>
                                </div>

                                <!-- Visibility options (hidden by default) -->
                                <div id="visibility-options" style="display: none; margin-top: 15px;">
                                    <p style="color: #aaa; margin-bottom: 15px;">Who do you want to see your profile? Simply select an option to control your visibility and searchability. Market your profile when and where you want.</p>

                                    <div style="display: flex; align-items: flex-start; padding: 20px; border: 1px solid #333; border-radius: 8px; margin-bottom: 10px;">
                                        <input type="radio" id="visibility-public" name="visibility" checked style="margin-right: 10px; margin-top: 3px;">
                                        <div>
                                            <label for="visibility-public" style="font-weight: 600; display: block; margin-bottom: 5px;">Public</label>
                                            <p style="color: #aaa; font-size: 14px;">Your profile is visible to the general public and will show up in search engine results.</p>
                                        </div>
                                    </div>

                                    <div style="display: flex; align-items: flex-start; padding: 20px; border: 1px solid #333; border-radius: 8px; margin-bottom: 10px;">
                                        <input type="radio" id="visibility-genius" name="visibility" style="margin-right: 10px; margin-top: 3px;">
                                        <div>
                                            <label for="visibility-genius" style="font-weight: 600; display: block; margin-bottom: 5px;">Genius Users Only</label>
                                            <p style="color: #aaa; font-size: 14px;">Only logged-in Genius users will see your profile.</p>
                                        </div>
                                    </div>

                                    <div style="display: flex; align-items: flex-start; padding: 20px; border: 1px solid #333; border-radius: 8px;">
                                        <input type="radio" id="visibility-private" name="visibility" style="margin-right: 10px; margin-top: 3px;">
                                        <div>
                                            <label for="visibility-private" style="font-weight: 600; display: block; margin-bottom: 5px;">Private</label>
                                            <p style="color: #aaa; font-size: 14px;">Your profile won't appear in any search results, not even on Genius. To view your profile, users must have a direct link and be logged in.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="profile-field">
                                <div class="profile-field-label">
                                    Custom profile URL
                                    <button class="profile-field-edit" id="edit-profile-url-btn">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                                        </svg>
                                    </button>
                                </div>
                                <div id="profile-url-display">/Genius/-01c9986eacd5e83fd8</div>

                                <!-- Edit form (hidden by default) -->
                                <div id="profile-url-edit" style="display: none; margin-top: 10px;">
                                    <div class="profile-url-form">
                                        <div class="profile-url-prefix">/Genius/</div>
                                        <input type="text" id="profile-url-input" class="profile-url-input" value="-01c9986eacd5e83fd8" placeholder="Enter your custom URL">
                                    </div>
                                    <div class="profile-help-text">This will be your username in GigGenius. Choose a name that represents you professionally.</div>
                                    <div class="profile-url-actions">
                                        <button class="btn btn-primary" id="save-profile-url-btn">Save</button>
                                        <button class="btn btn-secondary" id="cancel-profile-url-btn">Cancel</button>
                                    </div>
                                </div>
                            </div>

                            <div class="profile-field">
                                <div class="profile-field-label">
                                    Project preference
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="12" cy="12" r="10"></circle>
                                        <line x1="12" y1="16" x2="12" y2="12"></line>
                                        <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                    </svg>
                                </div>
                                <div class="profile-dropdown" id="project-preference-dropdown">
                                    <span>One-time project</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <polyline points="6 9 12 15 18 9"></polyline>
                                    </svg>
                                </div>

                                <!-- Project preference options (hidden by default) -->
                                <div id="project-preference-options" style="display: none; margin-top: 15px;">
                                    <div class="project-preference-option" data-value="one-time">
                                        <div class="project-preference-icon">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                                                <line x1="16" y1="2" x2="16" y2="6"></line>
                                                <line x1="8" y1="2" x2="8" y2="6"></line>
                                                <line x1="3" y1="10" x2="21" y2="10"></line>
                                            </svg>
                                        </div>
                                        <div class="project-preference-content">
                                            <div class="project-preference-title">One-time project</div>
                                            <div class="project-preference-description">A single project with a defined deliverable and end date.</div>
                                            <div class="project-preference-features">
                                                <div class="feature"><span class="feature-icon">✓</span> Fixed scope</div>
                                                <div class="feature"><span class="feature-icon">✓</span> Clear deliverables</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="project-preference-option" data-value="ongoing">
                                        <div class="project-preference-icon">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path>
                                                <path d="M12 6v6l4 2"></path>
                                            </svg>
                                        </div>
                                        <div class="project-preference-content">
                                            <div class="project-preference-title">Ongoing collaboration</div>
                                            <div class="project-preference-description">Regular but flexible work schedule with no fixed end date.</div>
                                            <div class="project-preference-features">
                                                <div class="feature"><span class="feature-icon">✓</span> Flexible schedule</div>
                                                <div class="feature"><span class="feature-icon">✓</span> Long-term partnership</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="profile-field">
                                <div class="profile-field-label">
                                    Earnings privacy
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="12" cy="12" r="10"></circle>
                                        <line x1="12" y1="16" x2="12" y2="12"></line>
                                        <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                    </svg>
                                </div>
                                <div class="profile-checkbox">
                                    <input type="checkbox" id="hide-earnings">
                                    <label for="hide-earnings">Hide my GigGenius earnings</label>
                                </div>
                                <div class="profile-help-text">This setting hides historical earnings on your profile. Your earnings will still be visible when you submit proposals or accept invitations to interview.</div>
                                <a href="#" class="profile-view-link">Learn more</a>
                            </div>
                        </div>

                        <div class="profile-section">
                            <h3 class="profile-section-title">Experience level</h3>

                            <div class="profile-radio-group">
                                <div class="profile-radio-option">
                                    <div class="radio-circle"></div>
                                    <div class="profile-radio-title">Entry level</div>
                                    <div class="profile-radio-description">I am relatively new to this field</div>
                                </div>

                                <div class="profile-radio-option">
                                    <div class="radio-circle"></div>
                                    <div class="profile-radio-title">Intermediate</div>
                                    <div class="profile-radio-description">I have substantial experience in this field</div>
                                </div>

                                <div class="profile-radio-option selected">
                                    <div class="radio-circle"></div>
                                    <div class="profile-radio-title">Expert</div>
                                    <div class="profile-radio-description">I have comprehensive and deep expertise in this field</div>
                                </div>
                            </div>
                        </div>

                        <div class="profile-section">
                            <div class="profile-specialized">
                                <div class="profile-specialized-title">Specialized profiles</div>
                                <div class="profile-specialized-count">2 out of 2 published</div>
                            </div>

                            <div class="profile-specialized-description">Create up to two different versions of your profile to more effectively highlight your individual specialties. <a href="#" class="profile-view-link">Learn more</a></div>

                            <div class="profile-specialized-item">
                                <div class="profile-specialized-item-title">Tech Support</div>
                                <div class="profile-specialized-item-status">Published</div>
                                <div class="profile-specialized-item-menu">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="12" cy="12" r="1"></circle>
                                        <circle cx="12" cy="5" r="1"></circle>
                                        <circle cx="12" cy="19" r="1"></circle>
                                    </svg>
                                </div>
                            </div>

                            <div class="profile-specialized-item">
                                <div class="profile-specialized-item-title">Ecommerce Website Development</div>
                                <div class="profile-specialized-item-status">Published</div>
                                <div class="profile-specialized-item-menu">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="12" cy="12" r="1"></circle>
                                        <circle cx="12" cy="5" r="1"></circle>
                                        <circle cx="12" cy="19" r="1"></circle>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div class="profile-section">
                            <div class="profile-field-label">
                                Categories
                                <button class="profile-field-edit">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                                    </svg>
                                </button>
                            </div>

                            <div class="profile-category">
                                <div class="profile-category-title">Customer Service</div>
                                <div class="profile-badge-container">
                                    <div class="profile-badge">Customer Service & Tech Support</div>
                                </div>
                            </div>

                            <div class="profile-category">
                                <div class="profile-category-title">Web, Mobile & Software Dev</div>
                                <div class="profile-badge-container">
                                    <div class="profile-badge">Web & Mobile Design</div>
                                    <div class="profile-badge">Web Development</div>
                                </div>
                            </div>

                            <div class="profile-category">
                                <div class="profile-category-title">Admin Support</div>
                                <div class="profile-badge-container">
                                    <div class="profile-badge">Virtual Assistance</div>
                                    <div class="profile-badge">Project Management</div>
                                </div>
                            </div>

                            <div class="profile-category">
                                <div class="profile-category-title">Sales & Marketing</div>
                                <div class="profile-badge-container">
                                    <div class="profile-badge">Digital Marketing</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Connected Services Section -->
                    <div id="services" class="content-section">
                        <h2 class="section-title">Connected services</h2>

                        <div class="card">
                            <div class="empty-state">
                                <div class="empty-state-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                                    </svg>
                                </div>
                                <div class="empty-state-text">You have no connected services yet</div>
                            </div>
                        </div>
                    </div>

                    <!-- Password & Security Section -->
                    <div id="security" class="content-section">
                        <h2 class="section-title">Password and security</h2>

                        <div class="card">
                            <div class="card-title">Login</div>

                            <div class="security-option">
                                <div class="security-option-title">Upwork password</div>
                                <div class="security-option-description">You've set an Upwork password. <a href="#" class="action-link">Change password</a></div>
                            </div>

                            <div class="security-option">
                                <div class="security-option-title">Log in with Google</div>
                                <div class="security-option-description">Not connected. You can choose to log in with Google.</div>
                                <button class="btn btn-secondary">Connect</button>
                            </div>

                            <div class="security-option">
                                <div class="security-option-title">Log in with Apple</div>
                                <div class="security-option-description">Not connected. You can choose to log in with Apple.</div>
                                <button class="btn btn-secondary">Connect</button>
                            </div>

                            <div class="divider"></div>

                            <div class="card-header">
                                <div class="card-title">Two-step verification</div>
                                <label class="toggle-switch">
                                    <input type="checkbox" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>

                            <div class="security-option">
                                <div class="security-option-title">Mobile app notifications</div>
                                <div class="security-option-description">Verify notifications with your Upwork mobile app.</div>
                                <label class="toggle-switch">
                                    <input type="checkbox">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>

                            <div class="security-option">
                                <div class="security-option-title">SMS text messages</div>
                                <div class="security-option-description">Verify one-time codes sent to your mobile number.</div>
                                <label class="toggle-switch">
                                    <input type="checkbox">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>

                            <div class="security-option">
                                <div class="security-option-title">Authenticator app codes</div>
                                <div class="security-option-description">Verify one-time codes generated in your preferred third party authenticator app.</div>
                                <label class="toggle-switch">
                                    <input type="checkbox">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>

                            <div class="security-option">
                                <div class="security-option-title">Security question and answer</div>
                                <div class="security-option-description">You've set up a question to answer when you can't use your mobile device for two-step verification.</div>
                                <a href="#" class="action-link">Update question and answer</a>
                            </div>
                        </div>
                    </div>

                    <!-- Identity Verification Section -->
                    <div id="verification" class="content-section">
                        <h2 class="section-title">Identity verification</h2>

                        <div class="card">
                            <div class="card-title">You verified your identity</div>
                            <div style="font-size: 14px; margin: 15px 0;">You've completed an important part of establishing trust in our global work marketplace.</div>

                            <div class="verification-step">
                                <div class="step-icon">✓</div>
                                <div class="step-details">
                                    <div class="step-title">Appear on camera</div>
                                    <div class="step-description">To show us it's really you, take an automatic selfie or join a video chat.</div>
                                </div>
                            </div>

                            <div class="verification-step">
                                <div class="step-icon">✓</div>
                                <div class="step-details">
                                    <div class="step-title">Show us a government-issued photo ID</div>
                                    <div class="step-description">We'll check that the country where your ID is from matches the country in your profile.</div>
                                </div>
                            </div>

                            <div class="verification-step">
                                <div class="step-icon">✓</div>
                                <div class="step-details">
                                    <div class="step-title">Submit for identity review</div>
                                    <div class="step-description">If we can't instantly verify you, we'll start a manual review process.</div>
                                </div>
                            </div>

                            <div style="background-color: rgba(0, 58, 140, 0.1); padding: 15px; border-radius: 8px; margin-top: 20px; display: flex; align-items: center;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-right: 10px; color: var(--primary-blue);">
                                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                                    <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                                </svg>
                                <div style="font-size: 14px;">
                                    We encrypt your data and will securely share it with our ID verification partner and any government that requires reporting income. <a href="#" class="action-link">Privacy Policy</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Notification Settings Section -->
                    <div id="notifications" class="content-section">
                        <h2 class="section-title">Notification settings</h2>

                        <div class="card">
                            <div class="tab-navigation">
                                <div class="tab active">Messages</div>
                                <div class="tab">Email updates</div>
                                <div class="tab">Tax settings</div>
                            </div>

                            <div class="card-title">Desktop</div>
                            <div style="margin: 15px 0;">
                                <div style="margin-bottom: 10px;">Show notifications for:</div>
                                <div class="dropdown-row">
                                    <div class="dropdown">
                                        <span>All activity</span>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <polyline points="6 9 12 15 18 9"></polyline>
                                        </svg>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <input type="checkbox" id="sound" checked>
                                        <label for="sound">Also play a sound</label>
                                    </div>
                                </div>

                                <div style="margin: 15px 0;">
                                    <div style="margin-bottom: 10px;">Increment message counter for:</div>
                                    <div class="dropdown">
                                        <span>All activity</span>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <polyline points="6 9 12 15 18 9"></polyline>
                                        </svg>
                                    </div>
                                </div>
                            </div>

                            <div class="divider"></div>

                            <div class="card-title">Mobile</div>
                            <div style="margin: 15px 0;">
                                <div style="margin-bottom: 10px;">Show notifications for:</div>
                                <div class="dropdown">
                                    <span>All activity</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <polyline points="6 9 12 15 18 9"></polyline>
                                    </svg>
                                </div>

                                <div style="margin: 15px 0;">
                                    <div style="margin-bottom: 10px;">Increment message counter for:</div>
                                    <div class="dropdown">
                                        <span>All activity</span>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <polyline points="6 9 12 15 18 9"></polyline>
                                        </svg>
                                    </div>
                                </div>
                            </div>

                            <div class="divider"></div>

                            <div class="card-title">Email</div>
                            <div style="margin: 15px 0;">
                                <div style="margin-bottom: 10px;">Sending to: <EMAIL></div>

                                <div style="margin: 15px 0;">
                                    <div style="margin-bottom: 10px;">Send an email with unread activity for:</div>
                                    <div class="dropdown-row">
                                        <div class="dropdown">
                                            <span>All activity</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <polyline points="6 9 12 15 18 9"></polyline>
                                            </svg>
                                        </div>
                                        <div class="dropdown">
                                            <span>Every 15 minutes</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <polyline points="6 9 12 15 18 9"></polyline>
                                            </svg>
                                        </div>
                                    </div>

                                    <div class="checkbox-row">
                                        <input type="checkbox" id="offline">
                                        <label for="offline">Only send when offline or idle</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer>
            <div class="container">
                <div class="footer-content">
                    <div class="footer-section">
                        <h3>For Clients</h3>
                        <a href="{{ url_for('how_to_hire') }}">How to Hire</a>
                        <a href="{{ url_for('marketplace') }}">Marketplace</a>
                        <a href="{{ url_for('payroll_services') }}">Payroll Services</a>
                        <a href="{{ url_for('service_catalog') }}">Service Catalog</a>
                        <a href="{{ url_for('business_networking') }}">Business Networking</a>
                        <a href="{{ url_for('ph_business_loan') }}">PH Business Loan</a>
                    </div>
                    <div class="footer-section">
                        <h3>For Geniuses</h3>
                        <a href="{{ url_for('how_it_works') }}">How It Works?</a>
                        <a href="{{ url_for('why_cant_apply') }}">Why Can't I Apply?</a>
                        <a href="{{ url_for('direct_contracts') }}">Direct Contracts</a>
                        <a href="{{ url_for('find_mentors') }}">Find Mentors</a>
                        <a href="{{ url_for('mentor_application') }}">Mentor Application</a>
                        <a href="{{ url_for('ph_health_insurance') }}">PH Health Insurance</a>
                        <a href="{{ url_for('ph_life_insurance') }}">PH Life Insurance</a>
                    </div>
                    <div class="footer-section">
                        <h3>Resources</h3>
                        <a href="{{ url_for('help_and_support') }}">Help & Support</a>
                        <a href="{{ url_for('news_and_events') }}">News & Events</a>
                        <a href="{{ url_for('affiliate_program') }}">Affiliate Program</a>
                    </div>
                    <div class="footer-section">
                        <h3>Company</h3>
                        <a href="{{ url_for('about_us') }}">About Us</a>
                        <a href="{{ url_for('contact_us') }}">Contact Us</a>
                        <a href="{{ url_for('charity_projects') }}">Charity Projects</a>
                    </div>
                </div>
                <div class="footer-bottom">
                    <div class="social-links-container">
                        <span>Follow Us:</span>
                        <div class="social-links">
                            <a href="https://www.facebook.com/giggenius.io" aria-label="Facebook"><i class="bi bi-facebook"></i></a>
                            <a href="https://www.instagram.com/giggenius.io/" aria-label="Instagram"><i class="bi bi-instagram"></i></a>
                            <a href="https://twitter.com/giggenius_io" aria-label="Twitter"><i class="bi bi-twitter-x"></i></a>
                            <a href="https://www.tiktok.com/@giggenius.io" aria-label="TikTok"><i class="bi bi-tiktok"></i></a>
                            <a href="https://www.youtube.com/@giggenius" aria-label="YouTube"><i class="bi bi-youtube"></i></a>
                            <a href="https://www.linkedin.com/company/gig-genius/" aria-label="LinkedIn"><i class="bi bi-linkedin"></i></a>
                        </div>
                    </div>
                    <p>©2025 GigGenius by <a href="https://genuinelysolutions.com/">Genuinely Business Solutions</a></p>
                    <div class="footer-links">
                        <a href="{{ url_for('terms_of_service') }}">Terms of Service</a>
                        <a href="{{ url_for('privacy_policy') }}">Privacy Policy</a>
                    </div>
                </div>
            </div>
        </footer>
    </div> <!-- End of container -->

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile Menu Toggle
            const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
            const mobileMenu = document.getElementById('mobileMenu');

            if (mobileMenuBtn && mobileMenu) {
                mobileMenuBtn.addEventListener('click', () => {
                    mobileMenu.classList.toggle('active');
                });

                // Close mobile menu when clicking outside
                document.addEventListener('click', (event) => {
                    if (!mobileMenuBtn.contains(event.target) && !mobileMenu.contains(event.target)) {
                        mobileMenu.classList.remove('active');
                    }
                });
            }

            // Search type dropdown
            const searchTypeBtn = document.getElementById('searchTypeBtn');
            const searchTypeDropdown = document.getElementById('searchTypeDropdown');
            const selectedSearchType = document.getElementById('selectedSearchType');
            const searchInput = document.getElementById('searchInput');

            if (searchTypeBtn && searchTypeDropdown) {
                // Toggle dropdown
                searchTypeBtn.addEventListener('click', function() {
                    searchTypeDropdown.classList.toggle('active');
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!searchTypeBtn.contains(e.target) && !searchTypeDropdown.contains(e.target)) {
                        searchTypeDropdown.classList.remove('active');
                    }
                });
            }

            // Close dropdowns when clicking outside
            window.addEventListener('click', function(e) {
                if (!e.target.matches('.nav-dropbtn')) {
                    const dropdowns = document.getElementsByClassName('nav-dropdown-content');
                    for (let dropdown of dropdowns) {
                        if (dropdown.classList.contains('show')) {
                            dropdown.classList.remove('show');
                        }
                    }
                }
            });

            // Profile dropdown
            const profileDropdown = document.querySelector('.profile-dropdown');
            if (profileDropdown) {
                const profileButton = profileDropdown.querySelector('.profile-button');

                // Toggle dropdown on profile button click
                if (profileButton) {
                    profileButton.addEventListener('click', function(e) {
                        e.stopPropagation();
                        profileDropdown.classList.toggle('active');
                    });
                }

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!profileDropdown.contains(e.target)) {
                        profileDropdown.classList.remove('active');
                    }
                });
            }

            // Handle sidebar menu item clicks
            const menuItems = document.querySelectorAll('.sidebar-menu li');
            const contentSections = document.querySelectorAll('.content-section');

            menuItems.forEach(item => {
                item.addEventListener('click', function(event) {
                    // Check if the clicked item has an anchor tag
                    const hasAnchor = this.querySelector('a');

                    // If there's an anchor tag, let the default navigation happen
                    if (hasAnchor) {
                        // Don't prevent default - allow navigation to the linked page
                        return;
                    }

                    // For items without anchor tags, handle the tab switching
                    // Update active menu item
                    menuItems.forEach(i => i.classList.remove('active'));
                    this.classList.add('active');

                    // Show corresponding content section
                    const sectionId = this.getAttribute('data-section');
                    if (sectionId && document.getElementById(sectionId)) {
                        contentSections.forEach(section => {
                            section.classList.remove('active');
                        });
                        document.getElementById(sectionId).classList.add('active');
                    }
                });
            });

            // Handle tab navigation
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabContainer = this.closest('.tab-navigation');
                    if (tabContainer) {
                        tabContainer.querySelectorAll('.tab').forEach(t => {
                            t.classList.remove('active');
                        });
                        this.classList.add('active');
                    }
                });
            });

            // Handle edit buttons
            const editButtons = document.querySelectorAll('.card-edit, .profile-field-edit:not(#edit-profile-url-btn)');
            editButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const cardTitle = this.closest('.card-header')?.querySelector('.card-title')?.textContent ||
                                      this.closest('.profile-field-label')?.textContent.trim();
                    alert('Editing: ' + cardTitle);
                });
            });

            // Handle profile URL edit
            const editProfileUrlBtn = document.getElementById('edit-profile-url-btn');
            const profileUrlDisplay = document.getElementById('profile-url-display');
            const profileUrlEdit = document.getElementById('profile-url-edit');
            const profileUrlInput = document.getElementById('profile-url-input');
            const saveProfileUrlBtn = document.getElementById('save-profile-url-btn');
            const cancelProfileUrlBtn = document.getElementById('cancel-profile-url-btn');

            if (editProfileUrlBtn && profileUrlDisplay && profileUrlEdit && profileUrlInput && saveProfileUrlBtn && cancelProfileUrlBtn) {
                // Show edit form when edit button is clicked
                editProfileUrlBtn.addEventListener('click', function() {
                    profileUrlDisplay.style.display = 'none';
                    profileUrlEdit.style.display = 'block';
                    profileUrlInput.focus();
                });

                // Save changes
                saveProfileUrlBtn.addEventListener('click', function() {
                    const newUrl = profileUrlInput.value.trim();
                    if (newUrl) {
                        profileUrlDisplay.textContent = '/Genius/' + newUrl;
                        profileUrlDisplay.style.display = 'block';
                        profileUrlEdit.style.display = 'none';
                    } else {
                        alert('Please enter a valid URL');
                    }
                });

                // Cancel editing
                cancelProfileUrlBtn.addEventListener('click', function() {
                    // Reset input to original value
                    const currentUrl = profileUrlDisplay.textContent;
                    profileUrlInput.value = currentUrl.replace('/Genius/', '');

                    profileUrlDisplay.style.display = 'block';
                    profileUrlEdit.style.display = 'none';
                });
            }

            // Handle action links
            const actionLinks = document.querySelectorAll('.action-link');
            actionLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    alert('Action: ' + this.textContent);
                });
            });

            // Handle buttons
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    alert('Button clicked: ' + this.textContent.trim());
                });
            });

            // Handle dropdowns (except project preference)
            const dropdowns = document.querySelectorAll('.dropdown:not(#project-preference-dropdown)');
            dropdowns.forEach(dropdown => {
                dropdown.addEventListener('click', function() {
                    const spanElement = this.querySelector('span');
                    if (spanElement) {
                        alert('Dropdown clicked: ' + spanElement.textContent);
                    }
                });
            });

            // Handle project preference dropdown
            const projectPreferenceDropdown = document.getElementById('project-preference-dropdown');
            const projectPreferenceOptions = document.getElementById('project-preference-options');
            const projectPreferenceOptionElements = document.querySelectorAll('.project-preference-option');

            if (projectPreferenceDropdown && projectPreferenceOptions) {
                // Show options when dropdown is clicked
                projectPreferenceDropdown.addEventListener('click', function() {
                    const isVisible = projectPreferenceOptions.style.display === 'block';
                    projectPreferenceOptions.style.display = isVisible ? 'none' : 'block';

                    // Mark the currently selected option
                    const currentValue = projectPreferenceDropdown.querySelector('span')?.textContent;
                    if (currentValue) {
                        projectPreferenceOptionElements.forEach(option => {
                            const optionTitle = option.querySelector('.project-preference-title')?.textContent;
                            if (optionTitle === currentValue) {
                                option.classList.add('selected');
                            } else {
                                option.classList.remove('selected');
                            }
                        });
                    }
                });

                // Handle option selection
                projectPreferenceOptionElements.forEach(option => {
                    option.addEventListener('click', function() {
                        const title = this.querySelector('.project-preference-title')?.textContent;
                        if (title && projectPreferenceDropdown.querySelector('span')) {
                            projectPreferenceDropdown.querySelector('span').textContent = title;

                            // Update selected state
                            projectPreferenceOptionElements.forEach(opt => {
                                opt.classList.remove('selected');
                            });
                            this.classList.add('selected');

                            // Hide options
                            projectPreferenceOptions.style.display = 'none';
                        }
                    });
                });

                // Close options when clicking outside
                document.addEventListener('click', function(event) {
                    if (projectPreferenceDropdown && projectPreferenceOptions &&
                        !projectPreferenceDropdown.contains(event.target) &&
                        !projectPreferenceOptions.contains(event.target)) {
                        projectPreferenceOptions.style.display = 'none';
                    }
                });
            }

            // Handle visibility dropdown
            const visibilityDropdown = document.getElementById('visibility-dropdown');
            const visibilityOptions = document.getElementById('visibility-options');

            if (visibilityDropdown && visibilityOptions) {
                visibilityDropdown.addEventListener('click', function(event) {
                    event.stopPropagation();
                    const isVisible = visibilityOptions.style.display === 'block';
                    visibilityOptions.style.display = isVisible ? 'none' : 'block';
                });

                // Handle radio button clicks in visibility options
                const visibilityRadios = document.querySelectorAll('input[name="visibility"]');
                visibilityRadios.forEach(radio => {
                    radio.addEventListener('change', function() {
                        if (this.checked) {
                            // Update the dropdown text
                            const label = document.querySelector(`label[for="${this.id}"]`)?.textContent;
                            if (label && visibilityDropdown.querySelector('span')) {
                                visibilityDropdown.querySelector('span').textContent = label;
                                // Hide the options after selection
                                setTimeout(() => {
                                    visibilityOptions.style.display = 'none';
                                }, 300);
                            }
                        }
                    });
                });

                // Close visibility options when clicking outside
                document.addEventListener('click', function(event) {
                    if (!visibilityDropdown.contains(event.target) && !visibilityOptions.contains(event.target)) {
                        visibilityOptions.style.display = 'none';
                    }
                });
            }
        });
    </script>
</body>
</html>